{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from \"../NoSsr/index.js\";\nimport Drawer, { getAnchor, isHorizontal } from \"../Drawer/Drawer.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTransitionProps } from \"../transitions/utils.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport SwipeArea from \"./SwipeArea.js\";\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    anchor = 'left',\n    disableBackdropTransition = false,\n    disableDiscovery = false,\n    disableSwipeToOpen = iOS,\n    hideBackdrop,\n    hysteresis = 0.52,\n    allowSwipeInChildren = false,\n    minFlingVelocity = 450,\n    ModalProps: {\n      BackdropProps,\n      ...ModalPropsProp\n    } = {},\n    onClose,\n    onOpen,\n    open = false,\n    PaperProps = {},\n    SwipeAreaProps,\n    swipeAreaWidth = 20,\n    transitionDuration = transitionDurationDefault,\n    variant = 'temporary',\n    // Mobile first.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].includes(anchorRtl) ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- claimedSwipeInstance is a singleton\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || paperRef.current?.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  const [SwipeAreaSlot, swipeAreaSlotProps] = useSlot('swipeArea', {\n    ref: swipeAreaRef,\n    elementType: SwipeArea,\n    ownerState: props,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        swipeArea: SwipeAreaProps,\n        ...slotProps\n      }\n    },\n    additionalProps: {\n      width: swipeAreaWidth,\n      anchor\n    }\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, {\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: {\n        BackdropProps: {\n          ...BackdropProps,\n          ref: backdropRef\n        },\n        // Ensures that paperRef.current will be defined inside the touch start event handler\n        // See https://github.com/mui/material-ui/issues/30414 for more information\n        ...(variant === 'temporary' && {\n          keepMounted: true\n        }),\n        ...ModalPropsProp\n      },\n      hideBackdrop: hideBackdrop,\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref,\n      slots: slots,\n      slotProps: {\n        ...slotProps,\n        backdrop: mergeSlotProps(slotProps.backdrop ?? BackdropProps, {\n          ref: backdropRef\n        }),\n        paper: mergeSlotProps(slotProps.paper ?? PaperProps, {\n          style: {\n            pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n          },\n          ref: handleRef\n        })\n      },\n      ...other\n    }), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeAreaSlot, {\n        ...swipeAreaSlotProps\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    docked: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    swipeArea: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    docked: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    swipeArea: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   * @deprecated use the `slotProps.swipeArea` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;", "map": {"version": 3, "names": ["React", "ReactDOM", "PropTypes", "elementTypeAcceptingRef", "NoSsr", "Drawer", "getAnchor", "isHorizontal", "useForkRef", "ownerDocument", "ownerWindow", "useEventCallback", "useEnhancedEffect", "useTheme", "useDefaultProps", "getTransitionProps", "mergeSlotProps", "useSlot", "SwipeArea", "jsx", "_jsx", "jsxs", "_jsxs", "UNCERTAINTY_THRESHOLD", "DRAG_STARTED_SIGNAL", "claimedSwipeInstance", "reset", "calculateCurrentX", "anchor", "touches", "doc", "body", "offsetWidth", "pageX", "calculateCurrentY", "containerWindow", "innerHeight", "clientY", "getMaxTranslate", "horizontalSwipe", "paperInstance", "clientWidth", "clientHeight", "getTranslate", "currentTranslate", "startLocation", "open", "maxTranslate", "Math", "min", "max", "getDomTreeShapes", "element", "rootNode", "domTreeShapes", "parentElement", "style", "getComputedStyle", "getPropertyValue", "scrollWidth", "scrollHeight", "push", "computeHasNativeHandler", "start", "current", "axisProperties", "scrollPosition", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "clientLength", "some", "shape", "goingForward", "axis", "round", "areNotAtStart", "areNotAtEnd", "iOS", "navigator", "test", "userAgent", "SwipeableDrawer", "forwardRef", "inProps", "ref", "props", "name", "theme", "transitionDurationDefault", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "disableBackdropTransition", "disableDiscovery", "disableSwipeToOpen", "hideBackdrop", "hysteresis", "allowSwipeInChildren", "minFlingVelocity", "ModalProps", "BackdropProps", "ModalPropsProp", "onClose", "onOpen", "PaperProps", "SwipeAreaProps", "swipe<PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "variant", "slots", "slotProps", "other", "maybeSwiping", "setMaybeSwiping", "useState", "swipeInstance", "useRef", "isSwiping", "swipeAreaRef", "backdropRef", "paperRef", "handleRef", "touchDetected", "calculatedDurationRef", "setPosition", "useCallback", "translate", "options", "mode", "changeTransition", "anchorRtl", "rtlTranslateMultiplier", "includes", "transform", "drawerStyle", "webkitTransform", "transition", "create", "easing", "undefined", "timeout", "webkitTransition", "backdropStyle", "opacity", "handleBodyTouchEnd", "nativeEvent", "flushSync", "horizontal", "changedTouches", "currentTarget", "startX", "startY", "translateRatio", "abs", "velocity", "startMaybeSwiping", "force", "lastTime", "lastTranslate", "paperHit", "handleBodyTouchMove", "currentX", "currentY", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dx", "dy", "definitelySwiping", "cancelable", "preventDefault", "performance", "now", "handleBodyTouchStart", "defaultPrevented", "defaultMuiPrevented", "useEffect", "addEventListener", "passive", "removeEventListener", "SwipeAreaSlot", "swipeAreaSlotProps", "elementType", "ownerState", "externalForwardedProps", "swipeArea", "additionalProps", "width", "Fragment", "children", "keepMounted", "backdrop", "paper", "pointerEvents", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "bool", "oneOf", "node", "number", "component", "isRequired", "object", "docked", "root", "appear"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/SwipeableDrawer/SwipeableDrawer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport NoSsr from \"../NoSsr/index.js\";\nimport Drawer, { getAnchor, isHorizontal } from \"../Drawer/Drawer.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useEnhancedEffect from \"../utils/useEnhancedEffect.js\";\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTransitionProps } from \"../transitions/utils.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport SwipeArea from \"./SwipeArea.js\";\n\n// This value is closed to what browsers are using internally to\n// trigger a native scroll.\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst UNCERTAINTY_THRESHOLD = 3; // px\n\n// This is the part of the drawer displayed on touch start.\nconst DRAG_STARTED_SIGNAL = 20; // px\n\n// We can only have one instance at the time claiming ownership for handling the swipe.\n// Otherwise, the UX would be confusing.\n// That's why we use a singleton here.\nlet claimedSwipeInstance = null;\n\n// Exported for test purposes.\nexport function reset() {\n  claimedSwipeInstance = null;\n}\nfunction calculateCurrentX(anchor, touches, doc) {\n  return anchor === 'right' ? doc.body.offsetWidth - touches[0].pageX : touches[0].pageX;\n}\nfunction calculateCurrentY(anchor, touches, containerWindow) {\n  return anchor === 'bottom' ? containerWindow.innerHeight - touches[0].clientY : touches[0].clientY;\n}\nfunction getMaxTranslate(horizontalSwipe, paperInstance) {\n  return horizontalSwipe ? paperInstance.clientWidth : paperInstance.clientHeight;\n}\nfunction getTranslate(currentTranslate, startLocation, open, maxTranslate) {\n  return Math.min(Math.max(open ? startLocation - currentTranslate : maxTranslate + startLocation - currentTranslate, 0), maxTranslate);\n}\n\n/**\n * @param {Element | null} element\n * @param {Element} rootNode\n */\nfunction getDomTreeShapes(element, rootNode) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L129\n  const domTreeShapes = [];\n  while (element && element !== rootNode.parentElement) {\n    const style = ownerWindow(rootNode).getComputedStyle(element);\n    if (\n    // Ignore the scroll children if the element is absolute positioned.\n    style.getPropertyValue('position') === 'absolute' ||\n    // Ignore the scroll children if the element has an overflowX hidden\n    style.getPropertyValue('overflow-x') === 'hidden') {\n      // noop\n    } else if (element.clientWidth > 0 && element.scrollWidth > element.clientWidth || element.clientHeight > 0 && element.scrollHeight > element.clientHeight) {\n      // Ignore the nodes that have no width.\n      // Keep elements with a scroll\n      domTreeShapes.push(element);\n    }\n    element = element.parentElement;\n  }\n  return domTreeShapes;\n}\n\n/**\n * @param {object} param0\n * @param {ReturnType<getDomTreeShapes>} param0.domTreeShapes\n */\nfunction computeHasNativeHandler({\n  domTreeShapes,\n  start,\n  current,\n  anchor\n}) {\n  // Adapted from https://github.com/oliviertassinari/react-swipeable-views/blob/7666de1dba253b896911adf2790ce51467670856/packages/react-swipeable-views/src/SwipeableViews.js#L175\n  const axisProperties = {\n    scrollPosition: {\n      x: 'scrollLeft',\n      y: 'scrollTop'\n    },\n    scrollLength: {\n      x: 'scrollWidth',\n      y: 'scrollHeight'\n    },\n    clientLength: {\n      x: 'clientWidth',\n      y: 'clientHeight'\n    }\n  };\n  return domTreeShapes.some(shape => {\n    // Determine if we are going backward or forward.\n    let goingForward = current >= start;\n    if (anchor === 'top' || anchor === 'left') {\n      goingForward = !goingForward;\n    }\n    const axis = anchor === 'left' || anchor === 'right' ? 'x' : 'y';\n    const scrollPosition = Math.round(shape[axisProperties.scrollPosition[axis]]);\n    const areNotAtStart = scrollPosition > 0;\n    const areNotAtEnd = scrollPosition + shape[axisProperties.clientLength[axis]] < shape[axisProperties.scrollLength[axis]];\n    if (goingForward && areNotAtEnd || !goingForward && areNotAtStart) {\n      return true;\n    }\n    return false;\n  });\n}\nconst iOS = typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);\nconst SwipeableDrawer = /*#__PURE__*/React.forwardRef(function SwipeableDrawer(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiSwipeableDrawer',\n    props: inProps\n  });\n  const theme = useTheme();\n  const transitionDurationDefault = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    anchor = 'left',\n    disableBackdropTransition = false,\n    disableDiscovery = false,\n    disableSwipeToOpen = iOS,\n    hideBackdrop,\n    hysteresis = 0.52,\n    allowSwipeInChildren = false,\n    minFlingVelocity = 450,\n    ModalProps: {\n      BackdropProps,\n      ...ModalPropsProp\n    } = {},\n    onClose,\n    onOpen,\n    open = false,\n    PaperProps = {},\n    SwipeAreaProps,\n    swipeAreaWidth = 20,\n    transitionDuration = transitionDurationDefault,\n    variant = 'temporary',\n    // Mobile first.\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const [maybeSwiping, setMaybeSwiping] = React.useState(false);\n  const swipeInstance = React.useRef({\n    isSwiping: null\n  });\n  const swipeAreaRef = React.useRef();\n  const backdropRef = React.useRef();\n  const paperRef = React.useRef();\n  const handleRef = useForkRef(PaperProps.ref, paperRef);\n  const touchDetected = React.useRef(false);\n\n  // Ref for transition duration based on / to match swipe speed\n  const calculatedDurationRef = React.useRef();\n\n  // Use a ref so the open value used is always up to date inside useCallback.\n  useEnhancedEffect(() => {\n    calculatedDurationRef.current = null;\n  }, [open]);\n  const setPosition = React.useCallback((translate, options = {}) => {\n    const {\n      mode = null,\n      changeTransition = true\n    } = options;\n    const anchorRtl = getAnchor(theme, anchor);\n    const rtlTranslateMultiplier = ['right', 'bottom'].includes(anchorRtl) ? 1 : -1;\n    const horizontalSwipe = isHorizontal(anchor);\n    const transform = horizontalSwipe ? `translate(${rtlTranslateMultiplier * translate}px, 0)` : `translate(0, ${rtlTranslateMultiplier * translate}px)`;\n    const drawerStyle = paperRef.current.style;\n    drawerStyle.webkitTransform = transform;\n    drawerStyle.transform = transform;\n    let transition = '';\n    if (mode) {\n      transition = theme.transitions.create('all', getTransitionProps({\n        easing: undefined,\n        style: undefined,\n        timeout: transitionDuration\n      }, {\n        mode\n      }));\n    }\n    if (changeTransition) {\n      drawerStyle.webkitTransition = transition;\n      drawerStyle.transition = transition;\n    }\n    if (!disableBackdropTransition && !hideBackdrop) {\n      const backdropStyle = backdropRef.current.style;\n      backdropStyle.opacity = 1 - translate / getMaxTranslate(horizontalSwipe, paperRef.current);\n      if (changeTransition) {\n        backdropStyle.webkitTransition = transition;\n        backdropStyle.transition = transition;\n      }\n    }\n  }, [anchor, disableBackdropTransition, hideBackdrop, theme, transitionDuration]);\n  const handleBodyTouchEnd = useEventCallback(nativeEvent => {\n    if (!touchDetected.current) {\n      return;\n    }\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- claimedSwipeInstance is a singleton\n    claimedSwipeInstance = null;\n    touchDetected.current = false;\n    ReactDOM.flushSync(() => {\n      setMaybeSwiping(false);\n    });\n\n    // The swipe wasn't started.\n    if (!swipeInstance.current.isSwiping) {\n      swipeInstance.current.isSwiping = null;\n      return;\n    }\n    swipeInstance.current.isSwiping = null;\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontal = isHorizontal(anchor);\n    let current;\n    if (horizontal) {\n      current = calculateCurrentX(anchorRtl, nativeEvent.changedTouches, ownerDocument(nativeEvent.currentTarget));\n    } else {\n      current = calculateCurrentY(anchorRtl, nativeEvent.changedTouches, ownerWindow(nativeEvent.currentTarget));\n    }\n    const startLocation = horizontal ? swipeInstance.current.startX : swipeInstance.current.startY;\n    const maxTranslate = getMaxTranslate(horizontal, paperRef.current);\n    const currentTranslate = getTranslate(current, startLocation, open, maxTranslate);\n    const translateRatio = currentTranslate / maxTranslate;\n    if (Math.abs(swipeInstance.current.velocity) > minFlingVelocity) {\n      // Calculate transition duration to match swipe speed\n      calculatedDurationRef.current = Math.abs((maxTranslate - currentTranslate) / swipeInstance.current.velocity) * 1000;\n    }\n    if (open) {\n      if (swipeInstance.current.velocity > minFlingVelocity || translateRatio > hysteresis) {\n        onClose();\n      } else {\n        // Reset the position, the swipe was aborted.\n        setPosition(0, {\n          mode: 'exit'\n        });\n      }\n      return;\n    }\n    if (swipeInstance.current.velocity < -minFlingVelocity || 1 - translateRatio > hysteresis) {\n      onOpen();\n    } else {\n      // Reset the position, the swipe was aborted.\n      setPosition(getMaxTranslate(horizontal, paperRef.current), {\n        mode: 'enter'\n      });\n    }\n  });\n  const startMaybeSwiping = (force = false) => {\n    if (!maybeSwiping) {\n      // on Safari Mobile, if you want to be able to have the 'click' event fired on child elements, nothing in the DOM can be changed.\n      // this is because Safari Mobile will not fire any mouse events (still fires touch though) if the DOM changes during mousemove.\n      // so do this change on first touchmove instead of touchstart\n      if (force || !(disableDiscovery && allowSwipeInChildren)) {\n        ReactDOM.flushSync(() => {\n          setMaybeSwiping(true);\n        });\n      }\n      const horizontalSwipe = isHorizontal(anchor);\n      if (!open && paperRef.current) {\n        // The ref may be null when a parent component updates while swiping.\n        setPosition(getMaxTranslate(horizontalSwipe, paperRef.current) + (disableDiscovery ? 15 : -DRAG_STARTED_SIGNAL), {\n          changeTransition: false\n        });\n      }\n      swipeInstance.current.velocity = 0;\n      swipeInstance.current.lastTime = null;\n      swipeInstance.current.lastTranslate = null;\n      swipeInstance.current.paperHit = false;\n      touchDetected.current = true;\n    }\n  };\n  const handleBodyTouchMove = useEventCallback(nativeEvent => {\n    // the ref may be null when a parent component updates while swiping\n    if (!paperRef.current || !touchDetected.current) {\n      return;\n    }\n\n    // We are not supposed to handle this touch move because the swipe was started in a scrollable container in the drawer\n    if (claimedSwipeInstance !== null && claimedSwipeInstance !== swipeInstance.current) {\n      return;\n    }\n    startMaybeSwiping(true);\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (open && paperRef.current.contains(nativeEvent.target) && claimedSwipeInstance === null) {\n      const domTreeShapes = getDomTreeShapes(nativeEvent.target, paperRef.current);\n      const hasNativeHandler = computeHasNativeHandler({\n        domTreeShapes,\n        start: horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY,\n        current: horizontalSwipe ? currentX : currentY,\n        anchor\n      });\n      if (hasNativeHandler) {\n        claimedSwipeInstance = true;\n        return;\n      }\n      claimedSwipeInstance = swipeInstance.current;\n    }\n\n    // We don't know yet.\n    if (swipeInstance.current.isSwiping == null) {\n      const dx = Math.abs(currentX - swipeInstance.current.startX);\n      const dy = Math.abs(currentY - swipeInstance.current.startY);\n      const definitelySwiping = horizontalSwipe ? dx > dy && dx > UNCERTAINTY_THRESHOLD : dy > dx && dy > UNCERTAINTY_THRESHOLD;\n      if (definitelySwiping && nativeEvent.cancelable) {\n        nativeEvent.preventDefault();\n      }\n      if (definitelySwiping === true || (horizontalSwipe ? dy > UNCERTAINTY_THRESHOLD : dx > UNCERTAINTY_THRESHOLD)) {\n        swipeInstance.current.isSwiping = definitelySwiping;\n        if (!definitelySwiping) {\n          handleBodyTouchEnd(nativeEvent);\n          return;\n        }\n\n        // Shift the starting point.\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n\n        // Compensate for the part of the drawer displayed on touch start.\n        if (!disableDiscovery && !open) {\n          if (horizontalSwipe) {\n            swipeInstance.current.startX -= DRAG_STARTED_SIGNAL;\n          } else {\n            swipeInstance.current.startY -= DRAG_STARTED_SIGNAL;\n          }\n        }\n      }\n    }\n    if (!swipeInstance.current.isSwiping) {\n      return;\n    }\n    const maxTranslate = getMaxTranslate(horizontalSwipe, paperRef.current);\n    let startLocation = horizontalSwipe ? swipeInstance.current.startX : swipeInstance.current.startY;\n    if (open && !swipeInstance.current.paperHit) {\n      startLocation = Math.min(startLocation, maxTranslate);\n    }\n    const translate = getTranslate(horizontalSwipe ? currentX : currentY, startLocation, open, maxTranslate);\n    if (open) {\n      if (!swipeInstance.current.paperHit) {\n        const paperHit = horizontalSwipe ? currentX < maxTranslate : currentY < maxTranslate;\n        if (paperHit) {\n          swipeInstance.current.paperHit = true;\n          swipeInstance.current.startX = currentX;\n          swipeInstance.current.startY = currentY;\n        } else {\n          return;\n        }\n      } else if (translate === 0) {\n        swipeInstance.current.startX = currentX;\n        swipeInstance.current.startY = currentY;\n      }\n    }\n    if (swipeInstance.current.lastTranslate === null) {\n      swipeInstance.current.lastTranslate = translate;\n      swipeInstance.current.lastTime = performance.now() + 1;\n    }\n    const velocity = (translate - swipeInstance.current.lastTranslate) / (performance.now() - swipeInstance.current.lastTime) * 1e3;\n\n    // Low Pass filter.\n    swipeInstance.current.velocity = swipeInstance.current.velocity * 0.4 + velocity * 0.6;\n    swipeInstance.current.lastTranslate = translate;\n    swipeInstance.current.lastTime = performance.now();\n\n    // We are swiping, let's prevent the scroll event on iOS.\n    if (nativeEvent.cancelable) {\n      nativeEvent.preventDefault();\n    }\n    setPosition(translate);\n  });\n  const handleBodyTouchStart = useEventCallback(nativeEvent => {\n    // We are not supposed to handle this touch move.\n    // Example of use case: ignore the event if there is a Slider.\n    if (nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    // We can only have one node at the time claiming ownership for handling the swipe.\n    if (nativeEvent.defaultMuiPrevented) {\n      return;\n    }\n\n    // At least one element clogs the drawer interaction zone.\n    if (open && (hideBackdrop || !backdropRef.current.contains(nativeEvent.target)) && !paperRef.current.contains(nativeEvent.target)) {\n      return;\n    }\n    const anchorRtl = getAnchor(theme, anchor);\n    const horizontalSwipe = isHorizontal(anchor);\n    const currentX = calculateCurrentX(anchorRtl, nativeEvent.touches, ownerDocument(nativeEvent.currentTarget));\n    const currentY = calculateCurrentY(anchorRtl, nativeEvent.touches, ownerWindow(nativeEvent.currentTarget));\n    if (!open) {\n      // logic for if swipe should be ignored:\n      // if disableSwipeToOpen\n      // if target != swipeArea, and target is not a child of paper ref\n      // if is a child of paper ref, and `allowSwipeInChildren` does not allow it\n      if (disableSwipeToOpen || !(nativeEvent.target === swipeAreaRef.current || paperRef.current?.contains(nativeEvent.target) && (typeof allowSwipeInChildren === 'function' ? allowSwipeInChildren(nativeEvent, swipeAreaRef.current, paperRef.current) : allowSwipeInChildren))) {\n        return;\n      }\n      if (horizontalSwipe) {\n        if (currentX > swipeAreaWidth) {\n          return;\n        }\n      } else if (currentY > swipeAreaWidth) {\n        return;\n      }\n    }\n    nativeEvent.defaultMuiPrevented = true;\n    claimedSwipeInstance = null;\n    swipeInstance.current.startX = currentX;\n    swipeInstance.current.startY = currentY;\n    startMaybeSwiping();\n  });\n  React.useEffect(() => {\n    if (variant === 'temporary') {\n      const doc = ownerDocument(paperRef.current);\n      doc.addEventListener('touchstart', handleBodyTouchStart);\n      // A blocking listener prevents Firefox's navbar to auto-hide on scroll.\n      // It only needs to prevent scrolling on the drawer's content when open.\n      // When closed, the overlay prevents scrolling.\n      doc.addEventListener('touchmove', handleBodyTouchMove, {\n        passive: !open\n      });\n      doc.addEventListener('touchend', handleBodyTouchEnd);\n      return () => {\n        doc.removeEventListener('touchstart', handleBodyTouchStart);\n        doc.removeEventListener('touchmove', handleBodyTouchMove, {\n          passive: !open\n        });\n        doc.removeEventListener('touchend', handleBodyTouchEnd);\n      };\n    }\n    return undefined;\n  }, [variant, open, handleBodyTouchStart, handleBodyTouchMove, handleBodyTouchEnd]);\n  React.useEffect(() => () => {\n    // We need to release the lock.\n    if (claimedSwipeInstance === swipeInstance.current) {\n      claimedSwipeInstance = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    if (!open) {\n      setMaybeSwiping(false);\n    }\n  }, [open]);\n  const [SwipeAreaSlot, swipeAreaSlotProps] = useSlot('swipeArea', {\n    ref: swipeAreaRef,\n    elementType: SwipeArea,\n    ownerState: props,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        swipeArea: SwipeAreaProps,\n        ...slotProps\n      }\n    },\n    additionalProps: {\n      width: swipeAreaWidth,\n      anchor\n    }\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(Drawer, {\n      open: variant === 'temporary' && maybeSwiping ? true : open,\n      variant: variant,\n      ModalProps: {\n        BackdropProps: {\n          ...BackdropProps,\n          ref: backdropRef\n        },\n        // Ensures that paperRef.current will be defined inside the touch start event handler\n        // See https://github.com/mui/material-ui/issues/30414 for more information\n        ...(variant === 'temporary' && {\n          keepMounted: true\n        }),\n        ...ModalPropsProp\n      },\n      hideBackdrop: hideBackdrop,\n      anchor: anchor,\n      transitionDuration: calculatedDurationRef.current || transitionDuration,\n      onClose: onClose,\n      ref: ref,\n      slots: slots,\n      slotProps: {\n        ...slotProps,\n        backdrop: mergeSlotProps(slotProps.backdrop ?? BackdropProps, {\n          ref: backdropRef\n        }),\n        paper: mergeSlotProps(slotProps.paper ?? PaperProps, {\n          style: {\n            pointerEvents: variant === 'temporary' && !open && !allowSwipeInChildren ? 'none' : ''\n          },\n          ref: handleRef\n        })\n      },\n      ...other\n    }), !disableSwipeToOpen && variant === 'temporary' && /*#__PURE__*/_jsx(NoSsr, {\n      children: /*#__PURE__*/_jsx(SwipeAreaSlot, {\n        ...swipeAreaSlotProps\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeableDrawer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If set to true, the swipe event will open the drawer even if the user begins the swipe on one of the drawer's children.\n   * This can be useful in scenarios where the drawer is partially visible.\n   * You can customize it further with a callback that determines which children the user can drag over to open the drawer\n   * (for example, to ignore other elements that handle touch move events, like sliders).\n   *\n   * @param {TouchEvent} event The 'touchstart' event\n   * @param {HTMLDivElement} swipeArea The swipe area element\n   * @param {HTMLDivElement} paper The drawer's paper element\n   *\n   * @default false\n   */\n  allowSwipeInChildren: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  anchor: PropTypes.oneOf(['bottom', 'left', 'right', 'top']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable the backdrop transition.\n   * This can improve the FPS on low-end devices.\n   * @default false\n   */\n  disableBackdropTransition: PropTypes.bool,\n  /**\n   * If `true`, touching the screen near the edge of the drawer will not slide in the drawer a bit\n   * to promote accidental discovery of the swipe gesture.\n   * @default false\n   */\n  disableDiscovery: PropTypes.bool,\n  /**\n   * If `true`, swipe to open is disabled. This is useful in browsers where swiping triggers\n   * navigation actions. Swipe to open is disabled on iOS browsers by default.\n   * @default typeof navigator !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent)\n   */\n  disableSwipeToOpen: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Affects how far the drawer must be opened/closed to change its state.\n   * Specified as percent (0-1) of the width of the drawer\n   * @default 0.52\n   */\n  hysteresis: PropTypes.number,\n  /**\n   * Defines, from which (average) velocity on, the swipe is\n   * defined as complete although hysteresis isn't reached.\n   * Good threshold is between 250 - 1000 px/s\n   * @default 450\n   */\n  minFlingVelocity: PropTypes.number,\n  /**\n   * @ignore\n   */\n  ModalProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    BackdropProps: PropTypes.shape({\n      component: elementTypeAcceptingRef\n    })\n  }),\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onClose: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the component requests to be opened.\n   *\n   * @param {React.SyntheticEvent<{}>} event The event source of the callback.\n   */\n  onOpen: PropTypes.func.isRequired,\n  /**\n   * If `true`, the component is shown.\n   * @default false\n   */\n  open: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  PaperProps: PropTypes /* @typescript-to-proptypes-ignore */.shape({\n    component: elementTypeAcceptingRef,\n    style: PropTypes.object\n  }),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    docked: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    swipeArea: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    docked: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    swipeArea: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The element is used to intercept the touch events on the edge.\n   * @deprecated use the `slotProps.swipeArea` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  SwipeAreaProps: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` that\n   * the drawer can be swiped open from.\n   * @default 20\n   */\n  swipeAreaWidth: PropTypes.number,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * @ignore\n   */\n  variant: PropTypes.oneOf(['permanent', 'persistent', 'temporary'])\n} : void 0;\nexport default SwipeableDrawer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,uBAAuB,MAAM,oCAAoC;AACxE,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,MAAM,IAAIC,SAAS,EAAEC,YAAY,QAAQ,qBAAqB;AACrE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;;AAEhC;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,IAAI;;AAE/B;AACA,OAAO,SAASC,KAAKA,CAAA,EAAG;EACtBD,oBAAoB,GAAG,IAAI;AAC7B;AACA,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC/C,OAAOF,MAAM,KAAK,OAAO,GAAGE,GAAG,CAACC,IAAI,CAACC,WAAW,GAAGH,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACI,KAAK;AACxF;AACA,SAASC,iBAAiBA,CAACN,MAAM,EAAEC,OAAO,EAAEM,eAAe,EAAE;EAC3D,OAAOP,MAAM,KAAK,QAAQ,GAAGO,eAAe,CAACC,WAAW,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO,GAAGR,OAAO,CAAC,CAAC,CAAC,CAACQ,OAAO;AACpG;AACA,SAASC,eAAeA,CAACC,eAAe,EAAEC,aAAa,EAAE;EACvD,OAAOD,eAAe,GAAGC,aAAa,CAACC,WAAW,GAAGD,aAAa,CAACE,YAAY;AACjF;AACA,SAASC,YAAYA,CAACC,gBAAgB,EAAEC,aAAa,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACzE,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,IAAI,GAAGD,aAAa,GAAGD,gBAAgB,GAAGG,YAAY,GAAGF,aAAa,GAAGD,gBAAgB,EAAE,CAAC,CAAC,EAAEG,YAAY,CAAC;AACvI;;AAEA;AACA;AACA;AACA;AACA,SAASI,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EAC3C;EACA,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAOF,OAAO,IAAIA,OAAO,KAAKC,QAAQ,CAACE,aAAa,EAAE;IACpD,MAAMC,KAAK,GAAG9C,WAAW,CAAC2C,QAAQ,CAAC,CAACI,gBAAgB,CAACL,OAAO,CAAC;IAC7D;IACA;IACAI,KAAK,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU;IACjD;IACAF,KAAK,CAACE,gBAAgB,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;MACjD;IAAA,CACD,MAAM,IAAIN,OAAO,CAACX,WAAW,GAAG,CAAC,IAAIW,OAAO,CAACO,WAAW,GAAGP,OAAO,CAACX,WAAW,IAAIW,OAAO,CAACV,YAAY,GAAG,CAAC,IAAIU,OAAO,CAACQ,YAAY,GAAGR,OAAO,CAACV,YAAY,EAAE;MAC1J;MACA;MACAY,aAAa,CAACO,IAAI,CAACT,OAAO,CAAC;IAC7B;IACAA,OAAO,GAAGA,OAAO,CAACG,aAAa;EACjC;EACA,OAAOD,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA,SAASQ,uBAAuBA,CAAC;EAC/BR,aAAa;EACbS,KAAK;EACLC,OAAO;EACPpC;AACF,CAAC,EAAE;EACD;EACA,MAAMqC,cAAc,GAAG;IACrBC,cAAc,EAAE;MACdC,CAAC,EAAE,YAAY;MACfC,CAAC,EAAE;IACL,CAAC;IACDC,YAAY,EAAE;MACZF,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL,CAAC;IACDE,YAAY,EAAE;MACZH,CAAC,EAAE,aAAa;MAChBC,CAAC,EAAE;IACL;EACF,CAAC;EACD,OAAOd,aAAa,CAACiB,IAAI,CAACC,KAAK,IAAI;IACjC;IACA,IAAIC,YAAY,GAAGT,OAAO,IAAID,KAAK;IACnC,IAAInC,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzC6C,YAAY,GAAG,CAACA,YAAY;IAC9B;IACA,MAAMC,IAAI,GAAG9C,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG;IAChE,MAAMsC,cAAc,GAAGlB,IAAI,CAAC2B,KAAK,CAACH,KAAK,CAACP,cAAc,CAACC,cAAc,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC7E,MAAME,aAAa,GAAGV,cAAc,GAAG,CAAC;IACxC,MAAMW,WAAW,GAAGX,cAAc,GAAGM,KAAK,CAACP,cAAc,CAACK,YAAY,CAACI,IAAI,CAAC,CAAC,GAAGF,KAAK,CAACP,cAAc,CAACI,YAAY,CAACK,IAAI,CAAC,CAAC;IACxH,IAAID,YAAY,IAAII,WAAW,IAAI,CAACJ,YAAY,IAAIG,aAAa,EAAE;MACjE,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;AACJ;AACA,MAAME,GAAG,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC5F,MAAMC,eAAe,GAAG,aAAalF,KAAK,CAACmF,UAAU,CAAC,SAASD,eAAeA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAMC,KAAK,GAAGxE,eAAe,CAAC;IAC5ByE,IAAI,EAAE,oBAAoB;IAC1BD,KAAK,EAAEF;EACT,CAAC,CAAC;EACF,MAAMI,KAAK,GAAG3E,QAAQ,CAAC,CAAC;EACxB,MAAM4E,yBAAyB,GAAG;IAChCC,KAAK,EAAEF,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAEN,KAAK,CAACG,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;IACJnE,MAAM,GAAG,MAAM;IACfoE,yBAAyB,GAAG,KAAK;IACjCC,gBAAgB,GAAG,KAAK;IACxBC,kBAAkB,GAAGpB,GAAG;IACxBqB,YAAY;IACZC,UAAU,GAAG,IAAI;IACjBC,oBAAoB,GAAG,KAAK;IAC5BC,gBAAgB,GAAG,GAAG;IACtBC,UAAU,EAAE;MACVC,aAAa;MACb,GAAGC;IACL,CAAC,GAAG,CAAC,CAAC;IACNC,OAAO;IACPC,MAAM;IACN7D,IAAI,GAAG,KAAK;IACZ8D,UAAU,GAAG,CAAC,CAAC;IACfC,cAAc;IACdC,cAAc,GAAG,EAAE;IACnBC,kBAAkB,GAAGtB,yBAAyB;IAC9CuB,OAAO,GAAG,WAAW;IACrB;IACAC,KAAK,GAAG,CAAC,CAAC;IACVC,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAG7B,KAAK;EACT,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAGrH,KAAK,CAACsH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMC,aAAa,GAAGvH,KAAK,CAACwH,MAAM,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMC,YAAY,GAAG1H,KAAK,CAACwH,MAAM,CAAC,CAAC;EACnC,MAAMG,WAAW,GAAG3H,KAAK,CAACwH,MAAM,CAAC,CAAC;EAClC,MAAMI,QAAQ,GAAG5H,KAAK,CAACwH,MAAM,CAAC,CAAC;EAC/B,MAAMK,SAAS,GAAGrH,UAAU,CAACoG,UAAU,CAACvB,GAAG,EAAEuC,QAAQ,CAAC;EACtD,MAAME,aAAa,GAAG9H,KAAK,CAACwH,MAAM,CAAC,KAAK,CAAC;;EAEzC;EACA,MAAMO,qBAAqB,GAAG/H,KAAK,CAACwH,MAAM,CAAC,CAAC;;EAE5C;EACA5G,iBAAiB,CAAC,MAAM;IACtBmH,qBAAqB,CAAC/D,OAAO,GAAG,IAAI;EACtC,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;EACV,MAAMkF,WAAW,GAAGhI,KAAK,CAACiI,WAAW,CAAC,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IACjE,MAAM;MACJC,IAAI,GAAG,IAAI;MACXC,gBAAgB,GAAG;IACrB,CAAC,GAAGF,OAAO;IACX,MAAMG,SAAS,GAAGhI,SAAS,CAACkF,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAM2G,sBAAsB,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACF,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/E,MAAM/F,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAM6G,SAAS,GAAGlG,eAAe,GAAG,aAAagG,sBAAsB,GAAGL,SAAS,QAAQ,GAAG,gBAAgBK,sBAAsB,GAAGL,SAAS,KAAK;IACrJ,MAAMQ,WAAW,GAAGd,QAAQ,CAAC5D,OAAO,CAACR,KAAK;IAC1CkF,WAAW,CAACC,eAAe,GAAGF,SAAS;IACvCC,WAAW,CAACD,SAAS,GAAGA,SAAS;IACjC,IAAIG,UAAU,GAAG,EAAE;IACnB,IAAIR,IAAI,EAAE;MACRQ,UAAU,GAAGpD,KAAK,CAACG,WAAW,CAACkD,MAAM,CAAC,KAAK,EAAE9H,kBAAkB,CAAC;QAC9D+H,MAAM,EAAEC,SAAS;QACjBvF,KAAK,EAAEuF,SAAS;QAChBC,OAAO,EAAEjC;MACX,CAAC,EAAE;QACDqB;MACF,CAAC,CAAC,CAAC;IACL;IACA,IAAIC,gBAAgB,EAAE;MACpBK,WAAW,CAACO,gBAAgB,GAAGL,UAAU;MACzCF,WAAW,CAACE,UAAU,GAAGA,UAAU;IACrC;IACA,IAAI,CAAC5C,yBAAyB,IAAI,CAACG,YAAY,EAAE;MAC/C,MAAM+C,aAAa,GAAGvB,WAAW,CAAC3D,OAAO,CAACR,KAAK;MAC/C0F,aAAa,CAACC,OAAO,GAAG,CAAC,GAAGjB,SAAS,GAAG5F,eAAe,CAACC,eAAe,EAAEqF,QAAQ,CAAC5D,OAAO,CAAC;MAC1F,IAAIqE,gBAAgB,EAAE;QACpBa,aAAa,CAACD,gBAAgB,GAAGL,UAAU;QAC3CM,aAAa,CAACN,UAAU,GAAGA,UAAU;MACvC;IACF;EACF,CAAC,EAAE,CAAChH,MAAM,EAAEoE,yBAAyB,EAAEG,YAAY,EAAEX,KAAK,EAAEuB,kBAAkB,CAAC,CAAC;EAChF,MAAMqC,kBAAkB,GAAGzI,gBAAgB,CAAC0I,WAAW,IAAI;IACzD,IAAI,CAACvB,aAAa,CAAC9D,OAAO,EAAE;MAC1B;IACF;IACA;IACAvC,oBAAoB,GAAG,IAAI;IAC3BqG,aAAa,CAAC9D,OAAO,GAAG,KAAK;IAC7B/D,QAAQ,CAACqJ,SAAS,CAAC,MAAM;MACvBjC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACE,aAAa,CAACvD,OAAO,CAACyD,SAAS,EAAE;MACpCF,aAAa,CAACvD,OAAO,CAACyD,SAAS,GAAG,IAAI;MACtC;IACF;IACAF,aAAa,CAACvD,OAAO,CAACyD,SAAS,GAAG,IAAI;IACtC,MAAMa,SAAS,GAAGhI,SAAS,CAACkF,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAM2H,UAAU,GAAGhJ,YAAY,CAACqB,MAAM,CAAC;IACvC,IAAIoC,OAAO;IACX,IAAIuF,UAAU,EAAE;MACdvF,OAAO,GAAGrC,iBAAiB,CAAC2G,SAAS,EAAEe,WAAW,CAACG,cAAc,EAAE/I,aAAa,CAAC4I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC9G,CAAC,MAAM;MACLzF,OAAO,GAAG9B,iBAAiB,CAACoG,SAAS,EAAEe,WAAW,CAACG,cAAc,EAAE9I,WAAW,CAAC2I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G;IACA,MAAM5G,aAAa,GAAG0G,UAAU,GAAGhC,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGnC,aAAa,CAACvD,OAAO,CAAC2F,MAAM;IAC9F,MAAM5G,YAAY,GAAGT,eAAe,CAACiH,UAAU,EAAE3B,QAAQ,CAAC5D,OAAO,CAAC;IAClE,MAAMpB,gBAAgB,GAAGD,YAAY,CAACqB,OAAO,EAAEnB,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACjF,MAAM6G,cAAc,GAAGhH,gBAAgB,GAAGG,YAAY;IACtD,IAAIC,IAAI,CAAC6G,GAAG,CAACtC,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,CAAC,GAAGxD,gBAAgB,EAAE;MAC/D;MACAyB,qBAAqB,CAAC/D,OAAO,GAAGhB,IAAI,CAAC6G,GAAG,CAAC,CAAC9G,YAAY,GAAGH,gBAAgB,IAAI2E,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,CAAC,GAAG,IAAI;IACrH;IACA,IAAIhH,IAAI,EAAE;MACR,IAAIyE,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,GAAGxD,gBAAgB,IAAIsD,cAAc,GAAGxD,UAAU,EAAE;QACpFM,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAsB,WAAW,CAAC,CAAC,EAAE;UACbI,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA;IACF;IACA,IAAIb,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,GAAG,CAACxD,gBAAgB,IAAI,CAAC,GAAGsD,cAAc,GAAGxD,UAAU,EAAE;MACzFO,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACL;MACAqB,WAAW,CAAC1F,eAAe,CAACiH,UAAU,EAAE3B,QAAQ,CAAC5D,OAAO,CAAC,EAAE;QACzDoE,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM2B,iBAAiB,GAAGA,CAACC,KAAK,GAAG,KAAK,KAAK;IAC3C,IAAI,CAAC5C,YAAY,EAAE;MACjB;MACA;MACA;MACA,IAAI4C,KAAK,IAAI,EAAE/D,gBAAgB,IAAII,oBAAoB,CAAC,EAAE;QACxDpG,QAAQ,CAACqJ,SAAS,CAAC,MAAM;UACvBjC,eAAe,CAAC,IAAI,CAAC;QACvB,CAAC,CAAC;MACJ;MACA,MAAM9E,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;MAC5C,IAAI,CAACkB,IAAI,IAAI8E,QAAQ,CAAC5D,OAAO,EAAE;QAC7B;QACAgE,WAAW,CAAC1F,eAAe,CAACC,eAAe,EAAEqF,QAAQ,CAAC5D,OAAO,CAAC,IAAIiC,gBAAgB,GAAG,EAAE,GAAG,CAACzE,mBAAmB,CAAC,EAAE;UAC/G6G,gBAAgB,EAAE;QACpB,CAAC,CAAC;MACJ;MACAd,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,GAAG,CAAC;MAClCvC,aAAa,CAACvD,OAAO,CAACiG,QAAQ,GAAG,IAAI;MACrC1C,aAAa,CAACvD,OAAO,CAACkG,aAAa,GAAG,IAAI;MAC1C3C,aAAa,CAACvD,OAAO,CAACmG,QAAQ,GAAG,KAAK;MACtCrC,aAAa,CAAC9D,OAAO,GAAG,IAAI;IAC9B;EACF,CAAC;EACD,MAAMoG,mBAAmB,GAAGzJ,gBAAgB,CAAC0I,WAAW,IAAI;IAC1D;IACA,IAAI,CAACzB,QAAQ,CAAC5D,OAAO,IAAI,CAAC8D,aAAa,CAAC9D,OAAO,EAAE;MAC/C;IACF;;IAEA;IACA,IAAIvC,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK8F,aAAa,CAACvD,OAAO,EAAE;MACnF;IACF;IACA+F,iBAAiB,CAAC,IAAI,CAAC;IACvB,MAAMzB,SAAS,GAAGhI,SAAS,CAACkF,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAMyI,QAAQ,GAAG1I,iBAAiB,CAAC2G,SAAS,EAAEe,WAAW,CAACxH,OAAO,EAAEpB,aAAa,CAAC4I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGpI,iBAAiB,CAACoG,SAAS,EAAEe,WAAW,CAACxH,OAAO,EAAEnB,WAAW,CAAC2I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI3G,IAAI,IAAI8E,QAAQ,CAAC5D,OAAO,CAACuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,IAAI/I,oBAAoB,KAAK,IAAI,EAAE;MAC1F,MAAM6B,aAAa,GAAGH,gBAAgB,CAACkG,WAAW,CAACmB,MAAM,EAAE5C,QAAQ,CAAC5D,OAAO,CAAC;MAC5E,MAAMyG,gBAAgB,GAAG3G,uBAAuB,CAAC;QAC/CR,aAAa;QACbS,KAAK,EAAExB,eAAe,GAAGgF,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGnC,aAAa,CAACvD,OAAO,CAAC2F,MAAM;QACpF3F,OAAO,EAAEzB,eAAe,GAAG8H,QAAQ,GAAGC,QAAQ;QAC9C1I;MACF,CAAC,CAAC;MACF,IAAI6I,gBAAgB,EAAE;QACpBhJ,oBAAoB,GAAG,IAAI;QAC3B;MACF;MACAA,oBAAoB,GAAG8F,aAAa,CAACvD,OAAO;IAC9C;;IAEA;IACA,IAAIuD,aAAa,CAACvD,OAAO,CAACyD,SAAS,IAAI,IAAI,EAAE;MAC3C,MAAMiD,EAAE,GAAG1H,IAAI,CAAC6G,GAAG,CAACQ,QAAQ,GAAG9C,aAAa,CAACvD,OAAO,CAAC0F,MAAM,CAAC;MAC5D,MAAMiB,EAAE,GAAG3H,IAAI,CAAC6G,GAAG,CAACS,QAAQ,GAAG/C,aAAa,CAACvD,OAAO,CAAC2F,MAAM,CAAC;MAC5D,MAAMiB,iBAAiB,GAAGrI,eAAe,GAAGmI,EAAE,GAAGC,EAAE,IAAID,EAAE,GAAGnJ,qBAAqB,GAAGoJ,EAAE,GAAGD,EAAE,IAAIC,EAAE,GAAGpJ,qBAAqB;MACzH,IAAIqJ,iBAAiB,IAAIvB,WAAW,CAACwB,UAAU,EAAE;QAC/CxB,WAAW,CAACyB,cAAc,CAAC,CAAC;MAC9B;MACA,IAAIF,iBAAiB,KAAK,IAAI,KAAKrI,eAAe,GAAGoI,EAAE,GAAGpJ,qBAAqB,GAAGmJ,EAAE,GAAGnJ,qBAAqB,CAAC,EAAE;QAC7GgG,aAAa,CAACvD,OAAO,CAACyD,SAAS,GAAGmD,iBAAiB;QACnD,IAAI,CAACA,iBAAiB,EAAE;UACtBxB,kBAAkB,CAACC,WAAW,CAAC;UAC/B;QACF;;QAEA;QACA9B,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;QACvC9C,aAAa,CAACvD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;;QAEvC;QACA,IAAI,CAACrE,gBAAgB,IAAI,CAACnD,IAAI,EAAE;UAC9B,IAAIP,eAAe,EAAE;YACnBgF,aAAa,CAACvD,OAAO,CAAC0F,MAAM,IAAIlI,mBAAmB;UACrD,CAAC,MAAM;YACL+F,aAAa,CAACvD,OAAO,CAAC2F,MAAM,IAAInI,mBAAmB;UACrD;QACF;MACF;IACF;IACA,IAAI,CAAC+F,aAAa,CAACvD,OAAO,CAACyD,SAAS,EAAE;MACpC;IACF;IACA,MAAM1E,YAAY,GAAGT,eAAe,CAACC,eAAe,EAAEqF,QAAQ,CAAC5D,OAAO,CAAC;IACvE,IAAInB,aAAa,GAAGN,eAAe,GAAGgF,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGnC,aAAa,CAACvD,OAAO,CAAC2F,MAAM;IACjG,IAAI7G,IAAI,IAAI,CAACyE,aAAa,CAACvD,OAAO,CAACmG,QAAQ,EAAE;MAC3CtH,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACJ,aAAa,EAAEE,YAAY,CAAC;IACvD;IACA,MAAMmF,SAAS,GAAGvF,YAAY,CAACJ,eAAe,GAAG8H,QAAQ,GAAGC,QAAQ,EAAEzH,aAAa,EAAEC,IAAI,EAAEC,YAAY,CAAC;IACxG,IAAID,IAAI,EAAE;MACR,IAAI,CAACyE,aAAa,CAACvD,OAAO,CAACmG,QAAQ,EAAE;QACnC,MAAMA,QAAQ,GAAG5H,eAAe,GAAG8H,QAAQ,GAAGtH,YAAY,GAAGuH,QAAQ,GAAGvH,YAAY;QACpF,IAAIoH,QAAQ,EAAE;UACZ5C,aAAa,CAACvD,OAAO,CAACmG,QAAQ,GAAG,IAAI;UACrC5C,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;UACvC9C,aAAa,CAACvD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;QACzC,CAAC,MAAM;UACL;QACF;MACF,CAAC,MAAM,IAAIpC,SAAS,KAAK,CAAC,EAAE;QAC1BX,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;QACvC9C,aAAa,CAACvD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;MACzC;IACF;IACA,IAAI/C,aAAa,CAACvD,OAAO,CAACkG,aAAa,KAAK,IAAI,EAAE;MAChD3C,aAAa,CAACvD,OAAO,CAACkG,aAAa,GAAGhC,SAAS;MAC/CX,aAAa,CAACvD,OAAO,CAACiG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxD;IACA,MAAMlB,QAAQ,GAAG,CAAC5B,SAAS,GAAGX,aAAa,CAACvD,OAAO,CAACkG,aAAa,KAAKa,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGzD,aAAa,CAACvD,OAAO,CAACiG,QAAQ,CAAC,GAAG,GAAG;;IAE/H;IACA1C,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,GAAGvC,aAAa,CAACvD,OAAO,CAAC8F,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;IACtFvC,aAAa,CAACvD,OAAO,CAACkG,aAAa,GAAGhC,SAAS;IAC/CX,aAAa,CAACvD,OAAO,CAACiG,QAAQ,GAAGc,WAAW,CAACC,GAAG,CAAC,CAAC;;IAElD;IACA,IAAI3B,WAAW,CAACwB,UAAU,EAAE;MAC1BxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IAC9B;IACA9C,WAAW,CAACE,SAAS,CAAC;EACxB,CAAC,CAAC;EACF,MAAM+C,oBAAoB,GAAGtK,gBAAgB,CAAC0I,WAAW,IAAI;IAC3D;IACA;IACA,IAAIA,WAAW,CAAC6B,gBAAgB,EAAE;MAChC;IACF;;IAEA;IACA,IAAI7B,WAAW,CAAC8B,mBAAmB,EAAE;MACnC;IACF;;IAEA;IACA,IAAIrI,IAAI,KAAKqD,YAAY,IAAI,CAACwB,WAAW,CAAC3D,OAAO,CAACuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,CAAC,IAAI,CAAC5C,QAAQ,CAAC5D,OAAO,CAACuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,EAAE;MACjI;IACF;IACA,MAAMlC,SAAS,GAAGhI,SAAS,CAACkF,KAAK,EAAE5D,MAAM,CAAC;IAC1C,MAAMW,eAAe,GAAGhC,YAAY,CAACqB,MAAM,CAAC;IAC5C,MAAMyI,QAAQ,GAAG1I,iBAAiB,CAAC2G,SAAS,EAAEe,WAAW,CAACxH,OAAO,EAAEpB,aAAa,CAAC4I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC5G,MAAMa,QAAQ,GAAGpI,iBAAiB,CAACoG,SAAS,EAAEe,WAAW,CAACxH,OAAO,EAAEnB,WAAW,CAAC2I,WAAW,CAACI,aAAa,CAAC,CAAC;IAC1G,IAAI,CAAC3G,IAAI,EAAE;MACT;MACA;MACA;MACA;MACA,IAAIoD,kBAAkB,IAAI,EAAEmD,WAAW,CAACmB,MAAM,KAAK9C,YAAY,CAAC1D,OAAO,IAAI4D,QAAQ,CAAC5D,OAAO,EAAEuG,QAAQ,CAAClB,WAAW,CAACmB,MAAM,CAAC,KAAK,OAAOnE,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAACgD,WAAW,EAAE3B,YAAY,CAAC1D,OAAO,EAAE4D,QAAQ,CAAC5D,OAAO,CAAC,GAAGqC,oBAAoB,CAAC,CAAC,EAAE;QAC7Q;MACF;MACA,IAAI9D,eAAe,EAAE;QACnB,IAAI8H,QAAQ,GAAGvD,cAAc,EAAE;UAC7B;QACF;MACF,CAAC,MAAM,IAAIwD,QAAQ,GAAGxD,cAAc,EAAE;QACpC;MACF;IACF;IACAuC,WAAW,CAAC8B,mBAAmB,GAAG,IAAI;IACtC1J,oBAAoB,GAAG,IAAI;IAC3B8F,aAAa,CAACvD,OAAO,CAAC0F,MAAM,GAAGW,QAAQ;IACvC9C,aAAa,CAACvD,OAAO,CAAC2F,MAAM,GAAGW,QAAQ;IACvCP,iBAAiB,CAAC,CAAC;EACrB,CAAC,CAAC;EACF/J,KAAK,CAACoL,SAAS,CAAC,MAAM;IACpB,IAAIpE,OAAO,KAAK,WAAW,EAAE;MAC3B,MAAMlF,GAAG,GAAGrB,aAAa,CAACmH,QAAQ,CAAC5D,OAAO,CAAC;MAC3ClC,GAAG,CAACuJ,gBAAgB,CAAC,YAAY,EAAEJ,oBAAoB,CAAC;MACxD;MACA;MACA;MACAnJ,GAAG,CAACuJ,gBAAgB,CAAC,WAAW,EAAEjB,mBAAmB,EAAE;QACrDkB,OAAO,EAAE,CAACxI;MACZ,CAAC,CAAC;MACFhB,GAAG,CAACuJ,gBAAgB,CAAC,UAAU,EAAEjC,kBAAkB,CAAC;MACpD,OAAO,MAAM;QACXtH,GAAG,CAACyJ,mBAAmB,CAAC,YAAY,EAAEN,oBAAoB,CAAC;QAC3DnJ,GAAG,CAACyJ,mBAAmB,CAAC,WAAW,EAAEnB,mBAAmB,EAAE;UACxDkB,OAAO,EAAE,CAACxI;QACZ,CAAC,CAAC;QACFhB,GAAG,CAACyJ,mBAAmB,CAAC,UAAU,EAAEnC,kBAAkB,CAAC;MACzD,CAAC;IACH;IACA,OAAOL,SAAS;EAClB,CAAC,EAAE,CAAC/B,OAAO,EAAElE,IAAI,EAAEmI,oBAAoB,EAAEb,mBAAmB,EAAEhB,kBAAkB,CAAC,CAAC;EAClFpJ,KAAK,CAACoL,SAAS,CAAC,MAAM,MAAM;IAC1B;IACA,IAAI3J,oBAAoB,KAAK8F,aAAa,CAACvD,OAAO,EAAE;MAClDvC,oBAAoB,GAAG,IAAI;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNzB,KAAK,CAACoL,SAAS,CAAC,MAAM;IACpB,IAAI,CAACtI,IAAI,EAAE;MACTuE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CAACvE,IAAI,CAAC,CAAC;EACV,MAAM,CAAC0I,aAAa,EAAEC,kBAAkB,CAAC,GAAGxK,OAAO,CAAC,WAAW,EAAE;IAC/DoE,GAAG,EAAEqC,YAAY;IACjBgE,WAAW,EAAExK,SAAS;IACtByK,UAAU,EAAErG,KAAK;IACjBsG,sBAAsB,EAAE;MACtB3E,KAAK;MACLC,SAAS,EAAE;QACT2E,SAAS,EAAEhF,cAAc;QACzB,GAAGK;MACL;IACF,CAAC;IACD4E,eAAe,EAAE;MACfC,KAAK,EAAEjF,cAAc;MACrBlF;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACtB,KAAK,CAACgM,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAa7K,IAAI,CAACf,MAAM,EAAE;MACnCyC,IAAI,EAAEkE,OAAO,KAAK,WAAW,IAAII,YAAY,GAAG,IAAI,GAAGtE,IAAI;MAC3DkE,OAAO,EAAEA,OAAO;MAChBT,UAAU,EAAE;QACVC,aAAa,EAAE;UACb,GAAGA,aAAa;UAChBnB,GAAG,EAAEsC;QACP,CAAC;QACD;QACA;QACA,IAAIX,OAAO,KAAK,WAAW,IAAI;UAC7BkF,WAAW,EAAE;QACf,CAAC,CAAC;QACF,GAAGzF;MACL,CAAC;MACDN,YAAY,EAAEA,YAAY;MAC1BvE,MAAM,EAAEA,MAAM;MACdmF,kBAAkB,EAAEgB,qBAAqB,CAAC/D,OAAO,IAAI+C,kBAAkB;MACvEL,OAAO,EAAEA,OAAO;MAChBrB,GAAG,EAAEA,GAAG;MACR4B,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAE;QACT,GAAGA,SAAS;QACZiF,QAAQ,EAAEnL,cAAc,CAACkG,SAAS,CAACiF,QAAQ,IAAI3F,aAAa,EAAE;UAC5DnB,GAAG,EAAEsC;QACP,CAAC,CAAC;QACFyE,KAAK,EAAEpL,cAAc,CAACkG,SAAS,CAACkF,KAAK,IAAIxF,UAAU,EAAE;UACnDpD,KAAK,EAAE;YACL6I,aAAa,EAAErF,OAAO,KAAK,WAAW,IAAI,CAAClE,IAAI,IAAI,CAACuD,oBAAoB,GAAG,MAAM,GAAG;UACtF,CAAC;UACDhB,GAAG,EAAEwC;QACP,CAAC;MACH,CAAC;MACD,GAAGV;IACL,CAAC,CAAC,EAAE,CAACjB,kBAAkB,IAAIc,OAAO,KAAK,WAAW,IAAI,aAAa5F,IAAI,CAAChB,KAAK,EAAE;MAC7E6L,QAAQ,EAAE,aAAa7K,IAAI,CAACoK,aAAa,EAAE;QACzC,GAAGC;MACL,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtH,eAAe,CAACuH,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEpG,oBAAoB,EAAEnG,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAAC0M,IAAI,CAAC,CAAC;EAC3E;AACF;AACA;EACEhL,MAAM,EAAE1B,SAAS,CAAC2M,KAAK,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D;AACF;AACA;EACEZ,QAAQ,EAAE/L,SAAS,CAAC4M,IAAI;EACxB;AACF;AACA;AACA;AACA;EACE9G,yBAAyB,EAAE9F,SAAS,CAAC0M,IAAI;EACzC;AACF;AACA;AACA;AACA;EACE3G,gBAAgB,EAAE/F,SAAS,CAAC0M,IAAI;EAChC;AACF;AACA;AACA;AACA;EACE1G,kBAAkB,EAAEhG,SAAS,CAAC0M,IAAI;EAClC;AACF;AACA;EACEzG,YAAY,EAAEjG,SAAS,CAAC0M,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACExG,UAAU,EAAElG,SAAS,CAAC6M,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;EACEzG,gBAAgB,EAAEpG,SAAS,CAAC6M,MAAM;EAClC;AACF;AACA;EACExG,UAAU,EAAErG,SAAS,CAAC,sCAAsCsE,KAAK,CAAC;IAChEgC,aAAa,EAAEtG,SAAS,CAACsE,KAAK,CAAC;MAC7BwI,SAAS,EAAE7M;IACb,CAAC;EACH,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEuG,OAAO,EAAExG,SAAS,CAACyM,IAAI,CAACM,UAAU;EAClC;AACF;AACA;AACA;AACA;EACEtG,MAAM,EAAEzG,SAAS,CAACyM,IAAI,CAACM,UAAU;EACjC;AACF;AACA;AACA;EACEnK,IAAI,EAAE5C,SAAS,CAAC0M,IAAI;EACpB;AACF;AACA;EACEhG,UAAU,EAAE1G,SAAS,CAAC,sCAAsCsE,KAAK,CAAC;IAChEwI,SAAS,EAAE7M,uBAAuB;IAClCqD,KAAK,EAAEtD,SAAS,CAACgN;EACnB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEhG,SAAS,EAAEhH,SAAS,CAACsE,KAAK,CAAC;IACzB2H,QAAQ,EAAEjM,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACgN,MAAM,CAAC,CAAC;IACjEC,MAAM,EAAEjN,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACgN,MAAM,CAAC,CAAC;IAC/Dd,KAAK,EAAElM,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACgN,MAAM,CAAC,CAAC;IAC9DE,IAAI,EAAElN,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACgN,MAAM,CAAC,CAAC;IAC7DrB,SAAS,EAAE3L,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACgN,MAAM,CAAC,CAAC;IAClEtE,UAAU,EAAE1I,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAACyM,IAAI,EAAEzM,SAAS,CAACgN,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEjG,KAAK,EAAE/G,SAAS,CAACsE,KAAK,CAAC;IACrB2H,QAAQ,EAAEjM,SAAS,CAACwL,WAAW;IAC/ByB,MAAM,EAAEjN,SAAS,CAACwL,WAAW;IAC7BU,KAAK,EAAElM,SAAS,CAACwL,WAAW;IAC5B0B,IAAI,EAAElN,SAAS,CAACwL,WAAW;IAC3BG,SAAS,EAAE3L,SAAS,CAACwL,WAAW;IAChC9C,UAAU,EAAE1I,SAAS,CAACwL;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE7E,cAAc,EAAE3G,SAAS,CAACgN,MAAM;EAChC;AACF;AACA;AACA;AACA;EACEpG,cAAc,EAAE5G,SAAS,CAAC6M,MAAM;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEhG,kBAAkB,EAAE7G,SAAS,CAACwM,SAAS,CAAC,CAACxM,SAAS,CAAC6M,MAAM,EAAE7M,SAAS,CAACsE,KAAK,CAAC;IACzE6I,MAAM,EAAEnN,SAAS,CAAC6M,MAAM;IACxBrH,KAAK,EAAExF,SAAS,CAAC6M,MAAM;IACvBjH,IAAI,EAAE5F,SAAS,CAAC6M;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACE/F,OAAO,EAAE9G,SAAS,CAAC2M,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3H,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}