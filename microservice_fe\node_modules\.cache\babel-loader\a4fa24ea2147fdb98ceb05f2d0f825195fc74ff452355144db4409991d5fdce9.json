{"ast": null, "code": "import { millisecondsInHour, millisecondsInMinute, millisecondsInSecond } from \"../../constants.js\";\nimport { numericPatterns } from \"./constants.js\";\nexport function mapValue(parseFnResult, mapFn) {\n  if (!parseFnResult) {\n    return parseFnResult;\n  }\n  return {\n    value: mapFn(parseFnResult.value),\n    rest: parseFnResult.rest\n  };\n}\nexport function parseNumericPattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n  if (!matchResult) {\n    return null;\n  }\n  return {\n    value: parseInt(matchResult[0], 10),\n    rest: dateString.slice(matchResult[0].length)\n  };\n}\nexport function parseTimezonePattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n  if (!matchResult) {\n    return null;\n  }\n\n  // Input is 'Z'\n  if (matchResult[0] === \"Z\") {\n    return {\n      value: 0,\n      rest: dateString.slice(1)\n    };\n  }\n  const sign = matchResult[1] === \"+\" ? 1 : -1;\n  const hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;\n  const minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;\n  const seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;\n  return {\n    value: sign * (hours * millisecondsInHour + minutes * millisecondsInMinute + seconds * millisecondsInSecond),\n    rest: dateString.slice(matchResult[0].length)\n  };\n}\nexport function parseAnyDigitsSigned(dateString) {\n  return parseNumericPattern(numericPatterns.anyDigitsSigned, dateString);\n}\nexport function parseNDigits(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigit, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigits, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigits, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigits, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\nexport function parseNDigitsSigned(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigitSigned, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigitsSigned, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigitsSigned, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigitsSigned, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^-?\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\nexport function dayPeriodEnumToHours(dayPeriod) {\n  switch (dayPeriod) {\n    case \"morning\":\n      return 4;\n    case \"evening\":\n      return 17;\n    case \"pm\":\n    case \"noon\":\n    case \"afternoon\":\n      return 12;\n    case \"am\":\n    case \"midnight\":\n    case \"night\":\n    default:\n      return 0;\n  }\n}\nexport function normalizeTwoDigitYear(twoDigitYear, currentYear) {\n  const isCommonEra = currentYear > 0;\n  // Absolute number of the current year:\n  // 1 -> 1 AC\n  // 0 -> 1 BC\n  // -1 -> 2 BC\n  const absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;\n  let result;\n  if (absCurrentYear <= 50) {\n    result = twoDigitYear || 100;\n  } else {\n    const rangeEnd = absCurrentYear + 50;\n    const rangeEndCentury = Math.trunc(rangeEnd / 100) * 100;\n    const isPreviousCentury = twoDigitYear >= rangeEnd % 100;\n    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);\n  }\n  return isCommonEra ? result : 1 - result;\n}\nexport function isLeapYearIndex(year) {\n  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}", "map": {"version": 3, "names": ["millisecondsInHour", "millisecondsInMinute", "millisecondsInSecond", "numericPatterns", "mapValue", "parseFnResult", "mapFn", "value", "rest", "parseNumericPattern", "pattern", "dateString", "matchResult", "match", "parseInt", "slice", "length", "parseTimezonePattern", "sign", "hours", "minutes", "seconds", "parseAnyDigitsSigned", "anyDigitsSigned", "parseNDigits", "n", "singleDigit", "twoDigits", "threeDigits", "fourDigits", "RegExp", "parseNDigitsSigned", "singleDigitSigned", "twoDigitsSigned", "threeDigitsSigned", "fourDigitsSigned", "dayPeriodEnumToHours", "<PERSON><PERSON><PERSON><PERSON>", "normalizeTwoDigitYear", "twoDigitYear", "currentYear", "isCommonEra", "absCurrentYear", "result", "rangeEnd", "rangeEndCentury", "Math", "trunc", "isPreviousCentury", "isLeapYearIndex", "year"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/date-fns/parse/_lib/utils.js"], "sourcesContent": ["import {\n  millisecondsInHour,\n  millisecondsInMinute,\n  millisecondsInSecond,\n} from \"../../constants.js\";\n\nimport { numericPatterns } from \"./constants.js\";\n\nexport function mapValue(parseFnResult, mapFn) {\n  if (!parseFnResult) {\n    return parseFnResult;\n  }\n\n  return {\n    value: mapFn(parseFnResult.value),\n    rest: parseFnResult.rest,\n  };\n}\n\nexport function parseNumericPattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n\n  if (!matchResult) {\n    return null;\n  }\n\n  return {\n    value: parseInt(matchResult[0], 10),\n    rest: dateString.slice(matchResult[0].length),\n  };\n}\n\nexport function parseTimezonePattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n\n  if (!matchResult) {\n    return null;\n  }\n\n  // Input is 'Z'\n  if (matchResult[0] === \"Z\") {\n    return {\n      value: 0,\n      rest: dateString.slice(1),\n    };\n  }\n\n  const sign = matchResult[1] === \"+\" ? 1 : -1;\n  const hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;\n  const minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;\n  const seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;\n\n  return {\n    value:\n      sign *\n      (hours * millisecondsInHour +\n        minutes * millisecondsInMinute +\n        seconds * millisecondsInSecond),\n    rest: dateString.slice(matchResult[0].length),\n  };\n}\n\nexport function parseAnyDigitsSigned(dateString) {\n  return parseNumericPattern(numericPatterns.anyDigitsSigned, dateString);\n}\n\nexport function parseNDigits(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigit, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigits, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigits, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigits, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\n\nexport function parseNDigitsSigned(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigitSigned, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigitsSigned, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigitsSigned, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigitsSigned, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^-?\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\n\nexport function dayPeriodEnumToHours(dayPeriod) {\n  switch (dayPeriod) {\n    case \"morning\":\n      return 4;\n    case \"evening\":\n      return 17;\n    case \"pm\":\n    case \"noon\":\n    case \"afternoon\":\n      return 12;\n    case \"am\":\n    case \"midnight\":\n    case \"night\":\n    default:\n      return 0;\n  }\n}\n\nexport function normalizeTwoDigitYear(twoDigitYear, currentYear) {\n  const isCommonEra = currentYear > 0;\n  // Absolute number of the current year:\n  // 1 -> 1 AC\n  // 0 -> 1 BC\n  // -1 -> 2 BC\n  const absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;\n\n  let result;\n  if (absCurrentYear <= 50) {\n    result = twoDigitYear || 100;\n  } else {\n    const rangeEnd = absCurrentYear + 50;\n    const rangeEndCentury = Math.trunc(rangeEnd / 100) * 100;\n    const isPreviousCentury = twoDigitYear >= rangeEnd % 100;\n    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);\n  }\n\n  return isCommonEra ? result : 1 - result;\n}\n\nexport function isLeapYearIndex(year) {\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n"], "mappings": "AAAA,SACEA,kBAAkB,EAClBC,oBAAoB,EACpBC,oBAAoB,QACf,oBAAoB;AAE3B,SAASC,eAAe,QAAQ,gBAAgB;AAEhD,OAAO,SAASC,QAAQA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAC7C,IAAI,CAACD,aAAa,EAAE;IAClB,OAAOA,aAAa;EACtB;EAEA,OAAO;IACLE,KAAK,EAAED,KAAK,CAACD,aAAa,CAACE,KAAK,CAAC;IACjCC,IAAI,EAAEH,aAAa,CAACG;EACtB,CAAC;AACH;AAEA,OAAO,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACvD,MAAMC,WAAW,GAAGD,UAAU,CAACE,KAAK,CAACH,OAAO,CAAC;EAE7C,IAAI,CAACE,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,OAAO;IACLL,KAAK,EAAEO,QAAQ,CAACF,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACnCJ,IAAI,EAAEG,UAAU,CAACI,KAAK,CAACH,WAAW,CAAC,CAAC,CAAC,CAACI,MAAM;EAC9C,CAAC;AACH;AAEA,OAAO,SAASC,oBAAoBA,CAACP,OAAO,EAAEC,UAAU,EAAE;EACxD,MAAMC,WAAW,GAAGD,UAAU,CAACE,KAAK,CAACH,OAAO,CAAC;EAE7C,IAAI,CAACE,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;;EAEA;EACA,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1B,OAAO;MACLL,KAAK,EAAE,CAAC;MACRC,IAAI,EAAEG,UAAU,CAACI,KAAK,CAAC,CAAC;IAC1B,CAAC;EACH;EAEA,MAAMG,IAAI,GAAGN,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5C,MAAMO,KAAK,GAAGP,WAAW,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EAC/D,MAAMQ,OAAO,GAAGR,WAAW,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EACjE,MAAMS,OAAO,GAAGT,WAAW,CAAC,CAAC,CAAC,GAAGE,QAAQ,CAACF,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;EAEjE,OAAO;IACLL,KAAK,EACHW,IAAI,IACHC,KAAK,GAAGnB,kBAAkB,GACzBoB,OAAO,GAAGnB,oBAAoB,GAC9BoB,OAAO,GAAGnB,oBAAoB,CAAC;IACnCM,IAAI,EAAEG,UAAU,CAACI,KAAK,CAACH,WAAW,CAAC,CAAC,CAAC,CAACI,MAAM;EAC9C,CAAC;AACH;AAEA,OAAO,SAASM,oBAAoBA,CAACX,UAAU,EAAE;EAC/C,OAAOF,mBAAmB,CAACN,eAAe,CAACoB,eAAe,EAAEZ,UAAU,CAAC;AACzE;AAEA,OAAO,SAASa,YAAYA,CAACC,CAAC,EAAEd,UAAU,EAAE;EAC1C,QAAQc,CAAC;IACP,KAAK,CAAC;MACJ,OAAOhB,mBAAmB,CAACN,eAAe,CAACuB,WAAW,EAAEf,UAAU,CAAC;IACrE,KAAK,CAAC;MACJ,OAAOF,mBAAmB,CAACN,eAAe,CAACwB,SAAS,EAAEhB,UAAU,CAAC;IACnE,KAAK,CAAC;MACJ,OAAOF,mBAAmB,CAACN,eAAe,CAACyB,WAAW,EAAEjB,UAAU,CAAC;IACrE,KAAK,CAAC;MACJ,OAAOF,mBAAmB,CAACN,eAAe,CAAC0B,UAAU,EAAElB,UAAU,CAAC;IACpE;MACE,OAAOF,mBAAmB,CAAC,IAAIqB,MAAM,CAAC,SAAS,GAAGL,CAAC,GAAG,GAAG,CAAC,EAAEd,UAAU,CAAC;EAC3E;AACF;AAEA,OAAO,SAASoB,kBAAkBA,CAACN,CAAC,EAAEd,UAAU,EAAE;EAChD,QAAQc,CAAC;IACP,KAAK,CAAC;MACJ,OAAOhB,mBAAmB,CAACN,eAAe,CAAC6B,iBAAiB,EAAErB,UAAU,CAAC;IAC3E,KAAK,CAAC;MACJ,OAAOF,mBAAmB,CAACN,eAAe,CAAC8B,eAAe,EAAEtB,UAAU,CAAC;IACzE,KAAK,CAAC;MACJ,OAAOF,mBAAmB,CAACN,eAAe,CAAC+B,iBAAiB,EAAEvB,UAAU,CAAC;IAC3E,KAAK,CAAC;MACJ,OAAOF,mBAAmB,CAACN,eAAe,CAACgC,gBAAgB,EAAExB,UAAU,CAAC;IAC1E;MACE,OAAOF,mBAAmB,CAAC,IAAIqB,MAAM,CAAC,WAAW,GAAGL,CAAC,GAAG,GAAG,CAAC,EAAEd,UAAU,CAAC;EAC7E;AACF;AAEA,OAAO,SAASyB,oBAAoBA,CAACC,SAAS,EAAE;EAC9C,QAAQA,SAAS;IACf,KAAK,SAAS;MACZ,OAAO,CAAC;IACV,KAAK,SAAS;MACZ,OAAO,EAAE;IACX,KAAK,IAAI;IACT,KAAK,MAAM;IACX,KAAK,WAAW;MACd,OAAO,EAAE;IACX,KAAK,IAAI;IACT,KAAK,UAAU;IACf,KAAK,OAAO;IACZ;MACE,OAAO,CAAC;EACZ;AACF;AAEA,OAAO,SAASC,qBAAqBA,CAACC,YAAY,EAAEC,WAAW,EAAE;EAC/D,MAAMC,WAAW,GAAGD,WAAW,GAAG,CAAC;EACnC;EACA;EACA;EACA;EACA,MAAME,cAAc,GAAGD,WAAW,GAAGD,WAAW,GAAG,CAAC,GAAGA,WAAW;EAElE,IAAIG,MAAM;EACV,IAAID,cAAc,IAAI,EAAE,EAAE;IACxBC,MAAM,GAAGJ,YAAY,IAAI,GAAG;EAC9B,CAAC,MAAM;IACL,MAAMK,QAAQ,GAAGF,cAAc,GAAG,EAAE;IACpC,MAAMG,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG;IACxD,MAAMI,iBAAiB,GAAGT,YAAY,IAAIK,QAAQ,GAAG,GAAG;IACxDD,MAAM,GAAGJ,YAAY,GAAGM,eAAe,IAAIG,iBAAiB,GAAG,GAAG,GAAG,CAAC,CAAC;EACzE;EAEA,OAAOP,WAAW,GAAGE,MAAM,GAAG,CAAC,GAAGA,MAAM;AAC1C;AAEA,OAAO,SAASM,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAOA,IAAI,GAAG,GAAG,KAAK,CAAC,IAAKA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAE;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}