{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = responsiveFontSizes;\nvar _formatMuiErrorMessage = _interopRequireDefault(require(\"@mui/utils/formatMuiErrorMessage\"));\nvar _cssUtils = require(\"./cssUtils\");\nfunction responsiveFontSizes(themeInput, options = {}) {\n  const {\n    breakpoints = ['sm', 'md', 'lg'],\n    disableAlign = false,\n    factor = 2,\n    variants = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'caption', 'button', 'overline']\n  } = options;\n  const theme = {\n    ...themeInput\n  };\n  theme.typography = {\n    ...theme.typography\n  };\n  const typography = theme.typography;\n\n  // Convert between CSS lengths e.g. em->px or px->rem\n  // Set the baseFontSize for your project. Defaults to 16px (also the browser default).\n  const convert = (0, _cssUtils.convertLength)(typography.htmlFontSize);\n  const breakpointValues = breakpoints.map(x => theme.breakpoints.values[x]);\n  variants.forEach(variant => {\n    const style = typography[variant];\n    if (!style) {\n      return;\n    }\n    const remFontSize = parseFloat(convert(style.fontSize, 'rem'));\n    if (remFontSize <= 1) {\n      return;\n    }\n    const maxFontSize = remFontSize;\n    const minFontSize = 1 + (maxFontSize - 1) / factor;\n    let {\n      lineHeight\n    } = style;\n    if (!(0, _cssUtils.isUnitless)(lineHeight) && !disableAlign) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: Unsupported non-unitless line height with grid alignment.\\n' + 'Use unitless line heights instead.' : (0, _formatMuiErrorMessage.default)(6));\n    }\n    if (!(0, _cssUtils.isUnitless)(lineHeight)) {\n      // make it unitless\n      lineHeight = parseFloat(convert(lineHeight, 'rem')) / parseFloat(remFontSize);\n    }\n    let transform = null;\n    if (!disableAlign) {\n      transform = value => (0, _cssUtils.alignProperty)({\n        size: value,\n        grid: (0, _cssUtils.fontGrid)({\n          pixels: 4,\n          lineHeight,\n          htmlFontSize: typography.htmlFontSize\n        })\n      });\n    }\n    typography[variant] = {\n      ...style,\n      ...(0, _cssUtils.responsiveProperty)({\n        cssProperty: 'fontSize',\n        min: minFontSize,\n        max: maxFontSize,\n        unit: 'rem',\n        breakpoints: breakpointValues,\n        transform\n      })\n    };\n  });\n  return theme;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "responsiveFontSizes", "_formatMuiErrorMessage", "_cssUtils", "themeInput", "options", "breakpoints", "disableAlign", "factor", "variants", "theme", "typography", "convert", "convertLength", "htmlFontSize", "breakpoint<PERSON><PERSON><PERSON>", "map", "x", "values", "for<PERSON>ach", "variant", "style", "remFontSize", "parseFloat", "fontSize", "maxFontSize", "minFontSize", "lineHeight", "isUnitless", "Error", "process", "env", "NODE_ENV", "transform", "alignProperty", "size", "grid", "fontGrid", "pixels", "responsiveProperty", "cssProperty", "min", "max", "unit"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/responsiveFontSizes.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = responsiveFontSizes;\nvar _formatMuiErrorMessage = _interopRequireDefault(require(\"@mui/utils/formatMuiErrorMessage\"));\nvar _cssUtils = require(\"./cssUtils\");\nfunction responsiveFontSizes(themeInput, options = {}) {\n  const {\n    breakpoints = ['sm', 'md', 'lg'],\n    disableAlign = false,\n    factor = 2,\n    variants = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'caption', 'button', 'overline']\n  } = options;\n  const theme = {\n    ...themeInput\n  };\n  theme.typography = {\n    ...theme.typography\n  };\n  const typography = theme.typography;\n\n  // Convert between CSS lengths e.g. em->px or px->rem\n  // Set the baseFontSize for your project. Defaults to 16px (also the browser default).\n  const convert = (0, _cssUtils.convertLength)(typography.htmlFontSize);\n  const breakpointValues = breakpoints.map(x => theme.breakpoints.values[x]);\n  variants.forEach(variant => {\n    const style = typography[variant];\n    if (!style) {\n      return;\n    }\n    const remFontSize = parseFloat(convert(style.fontSize, 'rem'));\n    if (remFontSize <= 1) {\n      return;\n    }\n    const maxFontSize = remFontSize;\n    const minFontSize = 1 + (maxFontSize - 1) / factor;\n    let {\n      lineHeight\n    } = style;\n    if (!(0, _cssUtils.isUnitless)(lineHeight) && !disableAlign) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: Unsupported non-unitless line height with grid alignment.\\n' + 'Use unitless line heights instead.' : (0, _formatMuiErrorMessage.default)(6));\n    }\n    if (!(0, _cssUtils.isUnitless)(lineHeight)) {\n      // make it unitless\n      lineHeight = parseFloat(convert(lineHeight, 'rem')) / parseFloat(remFontSize);\n    }\n    let transform = null;\n    if (!disableAlign) {\n      transform = value => (0, _cssUtils.alignProperty)({\n        size: value,\n        grid: (0, _cssUtils.fontGrid)({\n          pixels: 4,\n          lineHeight,\n          htmlFontSize: typography.htmlFontSize\n        })\n      });\n    }\n    typography[variant] = {\n      ...style,\n      ...(0, _cssUtils.responsiveProperty)({\n        cssProperty: 'fontSize',\n        min: minFontSize,\n        max: maxFontSize,\n        unit: 'rem',\n        breakpoints: breakpointValues,\n        transform\n      })\n    };\n  });\n  return theme;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,mBAAmB;AACrC,IAAIC,sBAAsB,GAAGR,sBAAsB,CAACC,OAAO,CAAC,kCAAkC,CAAC,CAAC;AAChG,IAAIQ,SAAS,GAAGR,OAAO,CAAC,YAAY,CAAC;AACrC,SAASM,mBAAmBA,CAACG,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACrD,MAAM;IACJC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAChCC,YAAY,GAAG,KAAK;IACpBC,MAAM,GAAG,CAAC;IACVC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU;EAC7H,CAAC,GAAGJ,OAAO;EACX,MAAMK,KAAK,GAAG;IACZ,GAAGN;EACL,CAAC;EACDM,KAAK,CAACC,UAAU,GAAG;IACjB,GAAGD,KAAK,CAACC;EACX,CAAC;EACD,MAAMA,UAAU,GAAGD,KAAK,CAACC,UAAU;;EAEnC;EACA;EACA,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAET,SAAS,CAACU,aAAa,EAAEF,UAAU,CAACG,YAAY,CAAC;EACrE,MAAMC,gBAAgB,GAAGT,WAAW,CAACU,GAAG,CAACC,CAAC,IAAIP,KAAK,CAACJ,WAAW,CAACY,MAAM,CAACD,CAAC,CAAC,CAAC;EAC1ER,QAAQ,CAACU,OAAO,CAACC,OAAO,IAAI;IAC1B,MAAMC,KAAK,GAAGV,UAAU,CAACS,OAAO,CAAC;IACjC,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,MAAMC,WAAW,GAAGC,UAAU,CAACX,OAAO,CAACS,KAAK,CAACG,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC9D,IAAIF,WAAW,IAAI,CAAC,EAAE;MACpB;IACF;IACA,MAAMG,WAAW,GAAGH,WAAW;IAC/B,MAAMI,WAAW,GAAG,CAAC,GAAG,CAACD,WAAW,GAAG,CAAC,IAAIjB,MAAM;IAClD,IAAI;MACFmB;IACF,CAAC,GAAGN,KAAK;IACT,IAAI,CAAC,CAAC,CAAC,EAAElB,SAAS,CAACyB,UAAU,EAAED,UAAU,CAAC,IAAI,CAACpB,YAAY,EAAE;MAC3D,MAAM,IAAIsB,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,kEAAkE,GAAG,oCAAoC,GAAG,CAAC,CAAC,EAAE9B,sBAAsB,CAACN,OAAO,EAAE,CAAC,CAAC,CAAC;IAC7M;IACA,IAAI,CAAC,CAAC,CAAC,EAAEO,SAAS,CAACyB,UAAU,EAAED,UAAU,CAAC,EAAE;MAC1C;MACAA,UAAU,GAAGJ,UAAU,CAACX,OAAO,CAACe,UAAU,EAAE,KAAK,CAAC,CAAC,GAAGJ,UAAU,CAACD,WAAW,CAAC;IAC/E;IACA,IAAIW,SAAS,GAAG,IAAI;IACpB,IAAI,CAAC1B,YAAY,EAAE;MACjB0B,SAAS,GAAGjC,KAAK,IAAI,CAAC,CAAC,EAAEG,SAAS,CAAC+B,aAAa,EAAE;QAChDC,IAAI,EAAEnC,KAAK;QACXoC,IAAI,EAAE,CAAC,CAAC,EAAEjC,SAAS,CAACkC,QAAQ,EAAE;UAC5BC,MAAM,EAAE,CAAC;UACTX,UAAU;UACVb,YAAY,EAAEH,UAAU,CAACG;QAC3B,CAAC;MACH,CAAC,CAAC;IACJ;IACAH,UAAU,CAACS,OAAO,CAAC,GAAG;MACpB,GAAGC,KAAK;MACR,GAAG,CAAC,CAAC,EAAElB,SAAS,CAACoC,kBAAkB,EAAE;QACnCC,WAAW,EAAE,UAAU;QACvBC,GAAG,EAAEf,WAAW;QAChBgB,GAAG,EAAEjB,WAAW;QAChBkB,IAAI,EAAE,KAAK;QACXrC,WAAW,EAAES,gBAAgB;QAC7BkB;MACF,CAAC;IACH,CAAC;EACH,CAAC,CAAC;EACF,OAAOvB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}