{"ast": null, "code": "export { default } from \"./borders.js\";\nexport * from \"./borders.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/borders/index.js"], "sourcesContent": ["export { default } from \"./borders.js\";\nexport * from \"./borders.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}