// Simple test to check if we can start the development server
const { spawn } = require('child_process');
const path = require('path');

console.log('Testing React app startup...');
console.log('Current directory:', process.cwd());
console.log('Node version:', process.version);

// Check if react is available
try {
  const react = require('react');
  console.log('✅ React is available, version:', react.version);
} catch (error) {
  console.log('❌ React is not available:', error.message);
}

// Check if react-scripts is available
try {
  const reactScriptsPath = path.join(__dirname, 'node_modules', 'react-scripts', 'scripts', 'start.js');
  console.log('React scripts path:', reactScriptsPath);
  
  const fs = require('fs');
  if (fs.existsSync(reactScriptsPath)) {
    console.log('✅ React scripts start.js exists');
  } else {
    console.log('❌ React scripts start.js does not exist');
  }
} catch (error) {
  console.log('❌ Error checking react-scripts:', error.message);
}

// Try to start the development server
console.log('\n🚀 Attempting to start development server...');

const startProcess = spawn('node', [
  path.join(__dirname, 'node_modules', 'react-scripts', 'scripts', 'start.js')
], {
  stdio: 'inherit',
  cwd: __dirname,
  env: { ...process.env, NODE_ENV: 'development' }
});

startProcess.on('error', (error) => {
  console.error('❌ Failed to start process:', error);
});

startProcess.on('exit', (code) => {
  console.log(`Process exited with code ${code}`);
});
