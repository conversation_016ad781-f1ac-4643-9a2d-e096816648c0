{"version": 3, "file": "child-loader.js", "sourceRoot": "", "sources": ["../../src/child/child-loader.ts"], "names": [], "mappings": ";;;;AACA,gCAAiD;AAEjD,IAAI,KAAgD,CAAC;AAErD,gBAAgB;AAChB,SAAgB,aAAa,CAC3B,MAAiD;IAEjD,KAAK,GAAG,MAAmD,CAAC;AAC9D,CAAC;AAJD,sCAIC;AAED,MAAM,KAAK,GAA8C;IACvD,OAAO,CAAC,GAAG,IAAgD;;QACzD,OAAO,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,mCAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD,IAAI,CAAC,GAAG,IAA6C;;QACnD,OAAO,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,mCAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3C,CAAC;IACD,SAAS,CAAC,GAAG,IAAkD;;QAC7D,OAAO,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,SAAS,mCAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAChD,CAAC;IACD,eAAe,CAAC,GAAG,IAAwD;;QACzE,OAAO,CAAC,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,eAAe,mCAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;CACF,CAAC;AAEF,gBAAgB;AACH,KACX,IAAA,6BAAuB,EAAC,KAAK,CAA8C,EAD9D,eAAO,eAAE,YAAI,YAAE,iBAAS,iBAAE,uBAAe,sBACsB", "sourcesContent": ["import type { NodeLoaderHooksAPI1, Node<PERSON>oaderHooksAPI2 } from '..';\nimport { filterHooksByAPIVersion } from '../esm';\n\nlet hooks: NodeLoaderHooksAPI1 & NodeLoaderHooksAPI2;\n\n/** @internal */\nexport function lateBindHooks(\n  _hooks: NodeLoaderHooksAPI1 | NodeLoaderHooksAPI2\n) {\n  hooks = _hooks as NodeLoaderHooksAPI1 & NodeLoaderHooksAPI2;\n}\n\nconst proxy: NodeLoaderHooksAPI1 & NodeLoaderHooksAPI2 = {\n  resolve(...args: Parameters<NodeLoaderHooksAPI1['resolve']>) {\n    return (hooks?.resolve ?? args[2])(...args);\n  },\n  load(...args: Parameters<NodeLoaderHooksAPI2['load']>) {\n    return (hooks?.load ?? args[2])(...args);\n  },\n  getFormat(...args: Parameters<NodeLoaderHooksAPI1['getFormat']>) {\n    return (hooks?.getFormat ?? args[2])(...args);\n  },\n  transformSource(...args: Parameters<NodeLoaderHooksAPI1['transformSource']>) {\n    return (hooks?.transformSource ?? args[2])(...args);\n  },\n};\n\n/** @internal */\nexport const { resolve, load, getFormat, transformSource } =\n  filterHooksByAPIVersion(proxy) as NodeLoaderHooksAPI1 & NodeLoaderHooksAPI2;\n"]}