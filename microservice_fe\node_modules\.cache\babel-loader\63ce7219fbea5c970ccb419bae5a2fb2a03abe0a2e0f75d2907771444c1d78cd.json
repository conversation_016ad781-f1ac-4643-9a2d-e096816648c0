{"ast": null, "code": "/**\n * @mui/private-theming v7.1.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport { default as ThemeProvider } from \"./ThemeProvider/index.js\";\nexport * from \"./ThemeProvider/index.js\";\nexport { default as useTheme } from \"./useTheme/index.js\";", "map": {"version": 3, "names": ["default", "ThemeProvider", "useTheme"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/private-theming/esm/index.js"], "sourcesContent": ["/**\n * @mui/private-theming v7.1.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport { default as ThemeProvider } from \"./ThemeProvider/index.js\";\nexport * from \"./ThemeProvider/index.js\";\nexport { default as useTheme } from \"./useTheme/index.js\";"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,IAAIC,aAAa,QAAQ,0BAA0B;AACnE,cAAc,0BAA0B;AACxC,SAASD,OAAO,IAAIE,QAAQ,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}