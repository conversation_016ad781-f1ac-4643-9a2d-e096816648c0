{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useTheme;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _system = require(\"@mui/system\");\nvar _defaultTheme = _interopRequireDefault(require(\"./defaultTheme\"));\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nfunction useTheme() {\n  const theme = (0, _system.useTheme)(_defaultTheme.default);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[_identifier.default] || theme;\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "useTheme", "React", "_system", "_defaultTheme", "_identifier", "theme", "process", "env", "NODE_ENV", "useDebugValue"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/useTheme.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useTheme;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _system = require(\"@mui/system\");\nvar _defaultTheme = _interopRequireDefault(require(\"./defaultTheme\"));\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nfunction useTheme() {\n  const theme = (0, _system.useTheme)(_defaultTheme.default);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n  return theme[_identifier.default] || theme;\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAGM,QAAQ;AAC1B,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,OAAO,GAAGT,OAAO,CAAC,aAAa,CAAC;AACpC,IAAIU,aAAa,GAAGX,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACrE,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AACjE,SAASO,QAAQA,CAAA,EAAG;EAClB,MAAMK,KAAK,GAAG,CAAC,CAAC,EAAEH,OAAO,CAACF,QAAQ,EAAEG,aAAa,CAACT,OAAO,CAAC;EAC1D,IAAIY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACAP,KAAK,CAACQ,aAAa,CAACJ,KAAK,CAAC;EAC5B;EACA,OAAOA,KAAK,CAACD,WAAW,CAACV,OAAO,CAAC,IAAIW,KAAK;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}