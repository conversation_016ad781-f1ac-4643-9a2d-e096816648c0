{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getPickersLocalization = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nconst getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: (0, _extends2.default)({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};\nexports.getPickersLocalization = getPickersLocalization;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "getPickersLocalization", "_extends2", "pickersTranslations", "components", "MuiLocalizationProvider", "defaultProps", "localeText"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/locales/utils/getPickersLocalization.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getPickersLocalization = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nconst getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: (0, _extends2.default)({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};\nexports.getPickersLocalization = getPickersLocalization;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,sBAAsB,GAAG,KAAK,CAAC;AACvC,IAAIC,SAAS,GAAGR,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,MAAMM,sBAAsB,GAAGE,mBAAmB,IAAI;EACpD,OAAO;IACLC,UAAU,EAAE;MACVC,uBAAuB,EAAE;QACvBC,YAAY,EAAE;UACZC,UAAU,EAAE,CAAC,CAAC,EAAEL,SAAS,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEO,mBAAmB;QAC5D;MACF;IACF;EACF,CAAC;AACH,CAAC;AACDJ,OAAO,CAACE,sBAAsB,GAAGA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}