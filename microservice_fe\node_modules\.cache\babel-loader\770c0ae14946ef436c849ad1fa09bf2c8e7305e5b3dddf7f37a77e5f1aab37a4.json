{"ast": null, "code": "export { useStaticPicker } from \"./useStaticPicker.js\";", "map": {"version": 3, "names": ["useStaticPicker"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useStaticPicker/index.js"], "sourcesContent": ["export { useStaticPicker } from \"./useStaticPicker.js\";"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}