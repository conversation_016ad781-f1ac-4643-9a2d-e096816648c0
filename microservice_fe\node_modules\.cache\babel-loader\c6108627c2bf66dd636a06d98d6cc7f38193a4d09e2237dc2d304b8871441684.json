{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = shouldSkipGeneratingVar;\nfunction shouldSkipGeneratingVar(keys) {\n  return !!keys[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!keys[1]?.match(/(mode|contrastThreshold|tonalOffset)/);\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "shouldSkipGeneratingVar", "keys", "match"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/shouldSkipGeneratingVar.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = shouldSkipGeneratingVar;\nfunction shouldSkipGeneratingVar(keys) {\n  return !!keys[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!keys[1]?.match(/(mode|contrastThreshold|tonalOffset)/);\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,uBAAuB;AACzC,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EACrC,OAAO,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,qGAAqG,CAAC,IAAI,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,WAAW,CAAC;EAC7J;EACAD,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,CAACA,IAAI,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,sCAAsC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}