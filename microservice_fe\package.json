{"name": "microservice_fe", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.4.0", "@types/node": "^16.18.126", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.5", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.0", "react-scripts": "5.0.1", "typescript": "^4.9.5"}, "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.5"}}