{"ast": null, "code": "import * as React from 'react';\nimport { isValidElementType } from 'react-is';\n\n// https://github.com/sindresorhus/is-plain-obj/blob/main/index.js\nexport function isPlainObject(item) {\n  if (typeof item !== 'object' || item === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(item);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);\n}\nfunction deepClone(source) {\n  if (/*#__PURE__*/React.isValidElement(source) || isValidElementType(source) || !isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\n\n/**\n * Merge objects deeply.\n * It will shallow copy React elements.\n *\n * If `options.clone` is set to `false` the source object will be merged directly into the target object.\n *\n * @example\n * ```ts\n * deepmerge({ a: { b: 1 }, d: 2 }, { a: { c: 2 }, d: 4 });\n * // => { a: { b: 1, c: 2 }, d: 4 }\n * ````\n *\n * @param target The target object.\n * @param source The source object.\n * @param options The merge options.\n * @param options.clone Set to `false` to merge the source object directly into the target object.\n * @returns The merged object.\n */\nexport default function deepmerge(target, source, options = {\n  clone: true\n}) {\n  const output = options.clone ? {\n    ...target\n  } : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (/*#__PURE__*/React.isValidElement(source[key]) || isValidElementType(source[key])) {\n        output[key] = source[key];\n      } else if (isPlainObject(source[key]) &&\n      // Avoid prototype pollution\n      Object.prototype.hasOwnProperty.call(target, key) && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}", "map": {"version": 3, "names": ["React", "isValidElementType", "isPlainObject", "item", "prototype", "Object", "getPrototypeOf", "Symbol", "toStringTag", "iterator", "deepClone", "source", "isValidElement", "output", "keys", "for<PERSON>ach", "key", "deepmerge", "target", "options", "clone", "hasOwnProperty", "call"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/deepmerge/deepmerge.js"], "sourcesContent": ["import * as React from 'react';\nimport { isValidElementType } from 'react-is';\n\n// https://github.com/sindresorhus/is-plain-obj/blob/main/index.js\nexport function isPlainObject(item) {\n  if (typeof item !== 'object' || item === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(item);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);\n}\nfunction deepClone(source) {\n  if (/*#__PURE__*/React.isValidElement(source) || isValidElementType(source) || !isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\n\n/**\n * Merge objects deeply.\n * It will shallow copy React elements.\n *\n * If `options.clone` is set to `false` the source object will be merged directly into the target object.\n *\n * @example\n * ```ts\n * deepmerge({ a: { b: 1 }, d: 2 }, { a: { c: 2 }, d: 4 });\n * // => { a: { b: 1, c: 2 }, d: 4 }\n * ````\n *\n * @param target The target object.\n * @param source The source object.\n * @param options The merge options.\n * @param options.clone Set to `false` to merge the source object directly into the target object.\n * @returns The merged object.\n */\nexport default function deepmerge(target, source, options = {\n  clone: true\n}) {\n  const output = options.clone ? {\n    ...target\n  } : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (/*#__PURE__*/React.isValidElement(source[key]) || isValidElementType(source[key])) {\n        output[key] = source[key];\n      } else if (isPlainObject(source[key]) &&\n      // Avoid prototype pollution\n      Object.prototype.hasOwnProperty.call(target, key) && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,UAAU;;AAE7C;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,cAAc,CAACH,IAAI,CAAC;EAC7C,OAAO,CAACC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKC,MAAM,CAACD,SAAS,IAAIC,MAAM,CAACC,cAAc,CAACF,SAAS,CAAC,KAAK,IAAI,KAAK,EAAEG,MAAM,CAACC,WAAW,IAAIL,IAAI,CAAC,IAAI,EAAEI,MAAM,CAACE,QAAQ,IAAIN,IAAI,CAAC;AAC3K;AACA,SAASO,SAASA,CAACC,MAAM,EAAE;EACzB,IAAI,aAAaX,KAAK,CAACY,cAAc,CAACD,MAAM,CAAC,IAAIV,kBAAkB,CAACU,MAAM,CAAC,IAAI,CAACT,aAAa,CAACS,MAAM,CAAC,EAAE;IACrG,OAAOA,MAAM;EACf;EACA,MAAME,MAAM,GAAG,CAAC,CAAC;EACjBR,MAAM,CAACS,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IACjCH,MAAM,CAACG,GAAG,CAAC,GAAGN,SAAS,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC;EACtC,CAAC,CAAC;EACF,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASI,SAASA,CAACC,MAAM,EAAEP,MAAM,EAAEQ,OAAO,GAAG;EAC1DC,KAAK,EAAE;AACT,CAAC,EAAE;EACD,MAAMP,MAAM,GAAGM,OAAO,CAACC,KAAK,GAAG;IAC7B,GAAGF;EACL,CAAC,GAAGA,MAAM;EACV,IAAIhB,aAAa,CAACgB,MAAM,CAAC,IAAIhB,aAAa,CAACS,MAAM,CAAC,EAAE;IAClDN,MAAM,CAACS,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;MACjC,IAAI,aAAahB,KAAK,CAACY,cAAc,CAACD,MAAM,CAACK,GAAG,CAAC,CAAC,IAAIf,kBAAkB,CAACU,MAAM,CAACK,GAAG,CAAC,CAAC,EAAE;QACrFH,MAAM,CAACG,GAAG,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;MAC3B,CAAC,MAAM,IAAId,aAAa,CAACS,MAAM,CAACK,GAAG,CAAC,CAAC;MACrC;MACAX,MAAM,CAACD,SAAS,CAACiB,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEF,GAAG,CAAC,IAAId,aAAa,CAACgB,MAAM,CAACF,GAAG,CAAC,CAAC,EAAE;QAC/E;QACAH,MAAM,CAACG,GAAG,CAAC,GAAGC,SAAS,CAACC,MAAM,CAACF,GAAG,CAAC,EAAEL,MAAM,CAACK,GAAG,CAAC,EAAEG,OAAO,CAAC;MAC5D,CAAC,MAAM,IAAIA,OAAO,CAACC,KAAK,EAAE;QACxBP,MAAM,CAACG,GAAG,CAAC,GAAGd,aAAa,CAACS,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGN,SAAS,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;MACjF,CAAC,MAAM;QACLH,MAAM,CAACG,GAAG,CAAC,GAAGL,MAAM,CAACK,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;EACA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}