{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ThemeProviderNoVars;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _system = require(\"@mui/system\");\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction ThemeProviderNoVars({\n  theme: themeInput,\n  ...props\n}) {\n  const scopedTheme = _identifier.default in themeInput ? themeInput[_identifier.default] : undefined;\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_system.ThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? _identifier.default : undefined,\n    theme: scopedTheme || themeInput\n  });\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "ThemeProviderNoVars", "React", "_system", "_identifier", "_jsxRuntime", "theme", "themeInput", "props", "scopedTheme", "undefined", "jsx", "ThemeProvider", "themeId"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/ThemeProviderNoVars.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ThemeProviderNoVars;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _system = require(\"@mui/system\");\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction ThemeProviderNoVars({\n  theme: themeInput,\n  ...props\n}) {\n  const scopedTheme = _identifier.default in themeInput ? themeInput[_identifier.default] : undefined;\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_system.ThemeProvider, {\n    ...props,\n    themeId: scopedTheme ? _identifier.default : undefined,\n    theme: scopedTheme || themeInput\n  });\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAGM,mBAAmB;AACrC,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,OAAO,GAAGT,OAAO,CAAC,aAAa,CAAC;AACpC,IAAIU,WAAW,GAAGX,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AACjE,IAAIW,WAAW,GAAGX,OAAO,CAAC,mBAAmB,CAAC;AAC9C,SAASO,mBAAmBA,CAAC;EAC3BK,KAAK,EAAEC,UAAU;EACjB,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,WAAW,GAAGL,WAAW,CAACT,OAAO,IAAIY,UAAU,GAAGA,UAAU,CAACH,WAAW,CAACT,OAAO,CAAC,GAAGe,SAAS;EACnG,OAAO,aAAa,CAAC,CAAC,EAAEL,WAAW,CAACM,GAAG,EAAER,OAAO,CAACS,aAAa,EAAE;IAC9D,GAAGJ,KAAK;IACRK,OAAO,EAAEJ,WAAW,GAAGL,WAAW,CAACT,OAAO,GAAGe,SAAS;IACtDJ,KAAK,EAAEG,WAAW,IAAIF;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}