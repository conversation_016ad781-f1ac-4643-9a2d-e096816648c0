{"version": 3, "file": "child-entrypoint.js", "sourceRoot": "", "sources": ["../../src/child/child-entrypoint.ts"], "names": [], "mappings": ";;AAAA,gCAAmD;AACnD,iDAAiE;AAEjE,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,wBAAS,CAAC;IAAE,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC/E,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,wBAAS,CAAC,MAAM,CAAC,CAAC;AAC9D,MAAM,KAAK,GAAG,IAAA,yBAAU,EAAC,aAAa,CAAmB,CAAC;AAE1D,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;AAC9B,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC;AAChC,KAAK,CAAC,eAAe,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAC1C,KAAK,CAAC,eAAe,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAEvD,8EAA8E;AAC9E,gGAAgG;AAChG,IAAI,KAAK,CAAC,KAAK,EAAE;IACf,MAAM,gBAAgB,GAAmB;QACvC,GAAG,KAAK;QACR,KAAK,EAAE,KAAK;KACb,CAAC;IACF,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,wBAAS,GAAG,IAAA,uBAAQ,EAAC,gBAAgB,CAAC,EAAE,CAAC;CAC7E;AAED,IAAA,eAAS,EAAC,KAAK,CAAC,CAAC", "sourcesContent": ["import { BootstrapState, bootstrap } from '../bin';\nimport { argPrefix, compress, decompress } from './argv-payload';\n\nconst base64ConfigArg = process.argv[2];\nif (!base64ConfigArg.startsWith(argPrefix)) throw new Error('unexpected argv');\nconst base64Payload = base64ConfigArg.slice(argPrefix.length);\nconst state = decompress(base64Payload) as BootstrapState;\n\nstate.isInChildProcess = true;\nstate.tsNodeScript = __filename;\nstate.parseArgvResult.argv = process.argv;\nstate.parseArgvResult.restArgs = process.argv.slice(3);\n\n// Modify and re-compress the payload delivered to subsequent child processes.\n// This logic may be refactored into bin.ts by https://github.com/TypeStrong/ts-node/issues/1831\nif (state.isCli) {\n  const stateForChildren: BootstrapState = {\n    ...state,\n    isCli: false,\n  };\n  state.parseArgvResult.argv[2] = `${argPrefix}${compress(stateForChildren)}`;\n}\n\nbootstrap(state);\n"]}