[{"D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx": "1", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx": "2", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts": "3", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts": "4", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx": "5", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx": "6", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx": "7", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx": "8", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx": "9", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx": "10", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx": "11", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx": "12", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx": "13", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx": "14", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts": "15", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts": "16", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts": "17", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts": "18", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts": "19", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts": "20", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts": "21", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts": "22", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts": "23", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts": "24", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts": "25", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts": "26", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts": "27", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts": "28", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts": "29", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts": "30", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts": "31", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts": "32", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts": "33", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts": "34", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts": "35", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts": "36", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx": "37", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx": "38", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx": "39", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx": "40", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx": "41", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx": "42", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts": "43", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractWorkSchedule.tsx": "44", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx": "45", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx": "46", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts": "47", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx": "48", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx": "49", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx": "50", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkingDatesPreview.tsx": "51", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx": "52", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx": "53", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx": "54", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx": "55", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx": "56", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx": "57", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx": "58", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx": "59", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx": "60", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts": "61", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx": "62", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx": "63", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts": "64", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx": "65", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx": "66"}, {"size": 591, "mtime": 1748515448800, "results": "67", "hashOfConfig": "68"}, {"size": 1793, "mtime": 1748515469191, "results": "69", "hashOfConfig": "68"}, {"size": 468, "mtime": 1747923851728, "results": "70", "hashOfConfig": "68"}, {"size": 138, "mtime": 1747902225279, "results": "71", "hashOfConfig": "68"}, {"size": 2839, "mtime": 1748514804001, "results": "72", "hashOfConfig": "68"}, {"size": 13504, "mtime": 1748522597222, "results": "73", "hashOfConfig": "68"}, {"size": 3735, "mtime": 1748527080783, "results": "74", "hashOfConfig": "68"}, {"size": 11876, "mtime": 1748527109195, "results": "75", "hashOfConfig": "68"}, {"size": 10742, "mtime": 1748452952220, "results": "76", "hashOfConfig": "68"}, {"size": 1078, "mtime": 1747902509716, "results": "77", "hashOfConfig": "68"}, {"size": 28838, "mtime": 1747994625869, "results": "78", "hashOfConfig": "68"}, {"size": 1722, "mtime": 1748514819539, "results": "79", "hashOfConfig": "68"}, {"size": 571, "mtime": 1747904248056, "results": "80", "hashOfConfig": "68"}, {"size": 605, "mtime": 1747902220275, "results": "81", "hashOfConfig": "68"}, {"size": 4372, "mtime": 1747932197380, "results": "82", "hashOfConfig": "68"}, {"size": 5865, "mtime": 1748527152322, "results": "83", "hashOfConfig": "68"}, {"size": 790, "mtime": 1747906005248, "results": "84", "hashOfConfig": "68"}, {"size": 5192, "mtime": 1748513900494, "results": "85", "hashOfConfig": "68"}, {"size": 1363, "mtime": 1747907859946, "results": "86", "hashOfConfig": "68"}, {"size": 24610, "mtime": 1748340655090, "results": "87", "hashOfConfig": "68"}, {"size": 213, "mtime": 1747922856213, "results": "88", "hashOfConfig": "68"}, {"size": 259, "mtime": 1747927809976, "results": "89", "hashOfConfig": "68"}, {"size": 488, "mtime": 1748243825884, "results": "90", "hashOfConfig": "68"}, {"size": 368, "mtime": 1747928054242, "results": "91", "hashOfConfig": "68"}, {"size": 330, "mtime": 1747911990280, "results": "92", "hashOfConfig": "68"}, {"size": 352, "mtime": 1747975523634, "results": "93", "hashOfConfig": "68"}, {"size": 3543, "mtime": 1747998520066, "results": "94", "hashOfConfig": "68"}, {"size": 15377, "mtime": 1748527852713, "results": "95", "hashOfConfig": "68"}, {"size": 252, "mtime": 1747922140447, "results": "96", "hashOfConfig": "68"}, {"size": 292, "mtime": 1747998489731, "results": "97", "hashOfConfig": "68"}, {"size": 155, "mtime": 1747901999771, "results": "98", "hashOfConfig": "68"}, {"size": 605, "mtime": 1748522597221, "results": "99", "hashOfConfig": "68"}, {"size": 346, "mtime": 1747902012555, "results": "100", "hashOfConfig": "68"}, {"size": 415, "mtime": 1747930255221, "results": "101", "hashOfConfig": "68"}, {"size": 1849, "mtime": 1747994411130, "results": "102", "hashOfConfig": "68"}, {"size": 4238, "mtime": 1747931208285, "results": "103", "hashOfConfig": "68"}, {"size": 10010, "mtime": 1748524578686, "results": "104", "hashOfConfig": "68"}, {"size": 14885, "mtime": 1748524723762, "results": "105", "hashOfConfig": "68"}, {"size": 2838, "mtime": 1747921471664, "results": "106", "hashOfConfig": "68"}, {"size": 21308, "mtime": 1748522597224, "results": "107", "hashOfConfig": "68"}, {"size": 8890, "mtime": 1748524634047, "results": "108", "hashOfConfig": "68"}, {"size": 6107, "mtime": 1748527011205, "results": "109", "hashOfConfig": "68"}, {"size": 2979, "mtime": 1748423808717, "results": "110", "hashOfConfig": "68"}, {"size": 7009, "mtime": 1748449027232, "results": "111", "hashOfConfig": "68"}, {"size": 3188, "mtime": 1747932337817, "results": "112", "hashOfConfig": "68"}, {"size": 7570, "mtime": 1748526924176, "results": "113", "hashOfConfig": "68"}, {"size": 948, "mtime": 1747902060729, "results": "114", "hashOfConfig": "68"}, {"size": 3892, "mtime": 1747910324864, "results": "115", "hashOfConfig": "68"}, {"size": 7271, "mtime": 1748526980031, "results": "116", "hashOfConfig": "68"}, {"size": 976, "mtime": 1748524477225, "results": "117", "hashOfConfig": "68"}, {"size": 6278, "mtime": 1748524679782, "results": "118", "hashOfConfig": "68"}, {"size": 2227, "mtime": 1748524457724, "results": "119", "hashOfConfig": "68"}, {"size": 6730, "mtime": 1748526940286, "results": "120", "hashOfConfig": "68"}, {"size": 1678, "mtime": 1748524469924, "results": "121", "hashOfConfig": "68"}, {"size": 564, "mtime": 1748524510466, "results": "122", "hashOfConfig": "68"}, {"size": 2042, "mtime": 1748526905685, "results": "123", "hashOfConfig": "68"}, {"size": 6203, "mtime": 1748526960217, "results": "124", "hashOfConfig": "68"}, {"size": 1303, "mtime": 1748524489455, "results": "125", "hashOfConfig": "68"}, {"size": 10874, "mtime": 1748423770679, "results": "126", "hashOfConfig": "68"}, {"size": 2257, "mtime": 1747912064565, "results": "127", "hashOfConfig": "68"}, {"size": 1351, "mtime": 1747932215756, "results": "128", "hashOfConfig": "68"}, {"size": 3267, "mtime": 1747978131060, "results": "129", "hashOfConfig": "68"}, {"size": 2361, "mtime": 1748527287211, "results": "130", "hashOfConfig": "68"}, {"size": 120, "mtime": 1747907779789, "results": "131", "hashOfConfig": "68"}, {"size": 5715, "mtime": 1748524444570, "results": "132", "hashOfConfig": "68"}, {"size": 12103, "mtime": 1748524827513, "results": "133", "hashOfConfig": "68"}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10nwx99", {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx", [], ["332"], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractWorkSchedule.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkingDatesPreview.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx", [], [], {"ruleId": "333", "severity": 1, "message": "334", "line": 592, "column": 6, "nodeType": "335", "endLine": 592, "endColumn": 8, "suggestions": "336", "suppressions": "337"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCustomerStatistics'. Either include it or remove the dependency array.", "ArrayExpression", ["338"], ["339"], {"desc": "340", "fix": "341"}, {"kind": "342", "justification": "343"}, "Update the dependencies array to be: [loadCustomerStatistics]", {"range": "344", "text": "345"}, "directive", "", [22117, 22119], "[loadCustomerStatistics]"]