{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ThemeProvider;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _ThemeProviderNoVars = _interopRequireDefault(require(\"./ThemeProviderNoVars\"));\nvar _ThemeProviderWithVars = require(\"./ThemeProviderWithVars\");\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction ThemeProvider({\n  theme,\n  ...props\n}) {\n  const noVarsTheme = React.useMemo(() => {\n    if (typeof theme === 'function') {\n      return theme;\n    }\n    const muiTheme = _identifier.default in theme ? theme[_identifier.default] : theme;\n    if (!('colorSchemes' in muiTheme)) {\n      if (!('vars' in muiTheme)) {\n        // For non-CSS variables themes, set `vars` to null to prevent theme inheritance from the upper theme.\n        // The example use case is the docs demo that uses ThemeProvider to customize the theme while the upper theme is using CSS variables.\n        return {\n          ...theme,\n          vars: null\n        };\n      }\n      return theme;\n    }\n    return null;\n  }, [theme]);\n  if (noVarsTheme) {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ThemeProviderNoVars.default, {\n      theme: noVarsTheme,\n      ...props\n    });\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ThemeProviderWithVars.CssVarsProvider, {\n    theme: theme,\n    ...props\n  });\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "ThemeProvider", "React", "_ThemeProviderNoVars", "_ThemeProviderWithVars", "_identifier", "_jsxRuntime", "theme", "props", "noVarsTheme", "useMemo", "muiTheme", "vars", "jsx", "CssVarsProvider"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/ThemeProvider.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = ThemeProvider;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _ThemeProviderNoVars = _interopRequireDefault(require(\"./ThemeProviderNoVars\"));\nvar _ThemeProviderWithVars = require(\"./ThemeProviderWithVars\");\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nfunction ThemeProvider({\n  theme,\n  ...props\n}) {\n  const noVarsTheme = React.useMemo(() => {\n    if (typeof theme === 'function') {\n      return theme;\n    }\n    const muiTheme = _identifier.default in theme ? theme[_identifier.default] : theme;\n    if (!('colorSchemes' in muiTheme)) {\n      if (!('vars' in muiTheme)) {\n        // For non-CSS variables themes, set `vars` to null to prevent theme inheritance from the upper theme.\n        // The example use case is the docs demo that uses ThemeProvider to customize the theme while the upper theme is using CSS variables.\n        return {\n          ...theme,\n          vars: null\n        };\n      }\n      return theme;\n    }\n    return null;\n  }, [theme]);\n  if (noVarsTheme) {\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ThemeProviderNoVars.default, {\n      theme: noVarsTheme,\n      ...props\n    });\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ThemeProviderWithVars.CssVarsProvider, {\n    theme: theme,\n    ...props\n  });\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACJ,OAAO,GAAGM,aAAa;AAC/B,IAAIC,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,oBAAoB,GAAGV,sBAAsB,CAACC,OAAO,CAAC,uBAAuB,CAAC,CAAC;AACnF,IAAIU,sBAAsB,GAAGV,OAAO,CAAC,yBAAyB,CAAC;AAC/D,IAAIW,WAAW,GAAGZ,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AACjE,IAAIY,WAAW,GAAGZ,OAAO,CAAC,mBAAmB,CAAC;AAC9C,SAASO,aAAaA,CAAC;EACrBM,KAAK;EACL,GAAGC;AACL,CAAC,EAAE;EACD,MAAMC,WAAW,GAAGP,KAAK,CAACQ,OAAO,CAAC,MAAM;IACtC,IAAI,OAAOH,KAAK,KAAK,UAAU,EAAE;MAC/B,OAAOA,KAAK;IACd;IACA,MAAMI,QAAQ,GAAGN,WAAW,CAACV,OAAO,IAAIY,KAAK,GAAGA,KAAK,CAACF,WAAW,CAACV,OAAO,CAAC,GAAGY,KAAK;IAClF,IAAI,EAAE,cAAc,IAAII,QAAQ,CAAC,EAAE;MACjC,IAAI,EAAE,MAAM,IAAIA,QAAQ,CAAC,EAAE;QACzB;QACA;QACA,OAAO;UACL,GAAGJ,KAAK;UACRK,IAAI,EAAE;QACR,CAAC;MACH;MACA,OAAOL,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,IAAIE,WAAW,EAAE;IACf,OAAO,aAAa,CAAC,CAAC,EAAEH,WAAW,CAACO,GAAG,EAAEV,oBAAoB,CAACR,OAAO,EAAE;MACrEY,KAAK,EAAEE,WAAW;MAClB,GAAGD;IACL,CAAC,CAAC;EACJ;EACA,OAAO,aAAa,CAAC,CAAC,EAAEF,WAAW,CAACO,GAAG,EAAET,sBAAsB,CAACU,eAAe,EAAE;IAC/EP,KAAK,EAAEA,KAAK;IACZ,GAAGC;EACL,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}