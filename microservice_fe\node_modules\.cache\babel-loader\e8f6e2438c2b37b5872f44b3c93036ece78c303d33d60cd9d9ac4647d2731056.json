{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useControlled from '@mui/utils/useControlled';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport clamp from '@mui/utils/clamp';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from \"../utils/areArraysEqual.js\";\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction getNewValue(currentValue, step, direction, min, max) {\n  return direction === 1 ? Math.min(currentValue + step, max) : Math.max(currentValue - step, min);\n}\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  const {\n    index: closestIndex\n  } = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null) ?? {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  const doc = ownerDocument(sliderRef.current);\n  if (!sliderRef.current?.contains(doc.activeElement) || Number(doc?.activeElement?.getAttribute('data-index')) !== activeIndex) {\n    sliderRef.current?.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  // lastChangedValue is updated whenever onChange is triggered.\n  const lastChangedValue = React.useRef(null);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue ?? min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    lastChangedValue.current = value;\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleRef = useForkRef(ref, sliderRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    if (isFocusVisible(event.target)) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers?.onFocus?.(event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers?.onBlur?.(event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue >= maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue <= marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, lastChangedValue.current ?? newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'PageUp', 'PageDown', 'Home', 'End'].includes(event.key)) {\n      event.preventDefault();\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      // Keys actions that change the value by more than the most granular `step`\n      // value are only applied if the step not `null`.\n      // When step is `null`, the `marks` prop is used instead to define valid values.\n      if (step != null) {\n        const stepSize = event.shiftKey ? shiftStep : step;\n        switch (event.key) {\n          case 'ArrowUp':\n            newValue = getNewValue(value, stepSize, 1, min, max);\n            break;\n          case 'ArrowRight':\n            newValue = getNewValue(value, stepSize, isRtl ? -1 : 1, min, max);\n            break;\n          case 'ArrowDown':\n            newValue = getNewValue(value, stepSize, -1, min, max);\n            break;\n          case 'ArrowLeft':\n            newValue = getNewValue(value, stepSize, isRtl ? 1 : -1, min, max);\n            break;\n          case 'PageUp':\n            newValue = getNewValue(value, shiftStep, 1, min, max);\n            break;\n          case 'PageDown':\n            newValue = getNewValue(value, shiftStep, -1, min, max);\n            break;\n          case 'Home':\n            newValue = min;\n            break;\n          case 'End':\n            newValue = max;\n            break;\n          default:\n            break;\n        }\n      } else if (marks) {\n        const maxMarksValue = marksValues[marksValues.length - 1];\n        const currentMarkIndex = marksValues.indexOf(value);\n        const decrementKeys = [isRtl ? 'ArrowRight' : 'ArrowLeft', 'ArrowDown', 'PageDown', 'Home'];\n        const incrementKeys = [isRtl ? 'ArrowLeft' : 'ArrowRight', 'ArrowUp', 'PageUp', 'End'];\n        if (decrementKeys.includes(event.key)) {\n          if (currentMarkIndex === 0) {\n            newValue = marksValues[0];\n          } else {\n            newValue = marksValues[currentMarkIndex - 1];\n          }\n        } else if (incrementKeys.includes(event.key)) {\n          if (currentMarkIndex === marksValues.length - 1) {\n            newValue = maxMarksValue;\n          } else {\n            newValue = marksValues[currentMarkIndex + 1];\n          }\n        }\n      }\n      if (newValue != null) {\n        changeValue(event, newValue);\n      }\n    }\n    otherHandlers?.onKeyDown?.(event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      document.activeElement?.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    otherHandlers.onChange?.(event);\n    // this handles value change by Pointer or Touch events\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.startsWith('vertical')) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.includes('-reverse')) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, lastChangedValue.current ?? newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    otherHandlers.onMouseDown?.(event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      ...externalProps,\n      ref: handleRef,\n      ...mergedEventHandlers\n    };\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    otherHandlers.onMouseOver?.(event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    otherHandlers.onMouseLeave?.(event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return {\n      ...externalProps,\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  let cssWritingMode;\n  if (orientation === 'vertical') {\n    cssWritingMode = isRtl ? 'vertical-rl' : 'vertical-lr';\n  }\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : parameters.step ?? undefined,\n      disabled,\n      ...externalProps,\n      ...mergedEventHandlers,\n      style: {\n        ...visuallyHidden,\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%',\n        writingMode: cssWritingMode\n      }\n    };\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}", "map": {"version": 3, "names": ["React", "ownerDocument", "useControlled", "useEnhancedEffect", "useEventCallback", "useForkRef", "isFocusVisible", "visuallyHidden", "clamp", "extractEventHandlers", "areArraysEqual", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "getNewValue", "currentValue", "step", "direction", "min", "max", "Math", "asc", "a", "b", "findClosest", "values", "index", "closestIndex", "reduce", "acc", "value", "distance", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "nearest", "round", "Number", "toFixed", "setValueIndex", "newValue", "output", "slice", "sort", "focusThumb", "sliderRef", "activeIndex", "setActive", "doc", "contains", "activeElement", "getAttribute", "querySelector", "focus", "areValuesEqual", "oldValue", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "rootRef", "ref", "scale", "shiftStep", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "lastChangedValue", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "focusedThumbIndex", "setFocusedThumbIndex", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "currentTarget", "target", "onFocus", "createHandleHiddenInputBlur", "onBlur", "changeValue", "valueInput", "marksIndex", "indexOf", "maxMarksValue", "Infinity", "previousValue", "createHandleHiddenInputKeyDown", "includes", "key", "preventDefault", "stepSize", "shift<PERSON>ey", "currentMarkIndex", "decrementKeys", "incrementKeys", "onKeyDown", "document", "blur", "createHandleHiddenInputChange", "valueAsNumber", "previousIndex", "axis", "getFingerNewValue", "finger", "move", "slider", "getBoundingClientRect", "startsWith", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "addEventListener", "passive", "useCallback", "removeEventListener", "useEffect", "createHandleMouseDown", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "externalProps", "externalHandlers", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "onMouseOver", "createHandleMouseLeave", "onMouseLeave", "getThumbProps", "getThumbStyle", "pointerEvents", "cssWritingMode", "getHiddenInputProps", "style", "writingMode"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/Slider/useSlider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport useControlled from '@mui/utils/useControlled';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useForkRef from '@mui/utils/useForkRef';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport clamp from '@mui/utils/clamp';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport areArraysEqual from \"../utils/areArraysEqual.js\";\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction getNewValue(currentValue, step, direction, min, max) {\n  return direction === 1 ? Math.min(currentValue + step, max) : Math.max(currentValue - step, min);\n}\nfunction asc(a, b) {\n  return a - b;\n}\nfunction findClosest(values, currentValue) {\n  const {\n    index: closestIndex\n  } = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null) ?? {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  }\n\n  // The event is MouseEvent\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  const doc = ownerDocument(sliderRef.current);\n  if (!sliderRef.current?.contains(doc.activeElement) || Number(doc?.activeElement?.getAttribute('data-index')) !== activeIndex) {\n    sliderRef.current?.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nfunction areValuesEqual(newValue, oldValue) {\n  if (typeof newValue === 'number' && typeof oldValue === 'number') {\n    return newValue === oldValue;\n  }\n  if (typeof newValue === 'object' && typeof oldValue === 'object') {\n    return areArraysEqual(newValue, oldValue);\n  }\n  return false;\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x;\n\n// TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\nexport function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    rootRef: ref,\n    scale = Identity,\n    step = 1,\n    shiftStep = 10,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(undefined);\n  // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  // lastChangedValue is updated whenever onChange is triggered.\n  const lastChangedValue = React.useRef(null);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue ?? min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event;\n    // @ts-ignore The nativeEvent is function, not object\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    lastChangedValue.current = value;\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => value == null ? min : clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef(null);\n  const handleRef = useForkRef(ref, sliderRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    if (isFocusVisible(event.target)) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers?.onFocus?.(event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    if (!isFocusVisible(event.target)) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers?.onBlur?.(event);\n  };\n  const changeValue = (event, valueInput) => {\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value);\n    let newValue = valueInput;\n    if (marks && step == null) {\n      const maxMarksValue = marksValues[marksValues.length - 1];\n      if (newValue >= maxMarksValue) {\n        newValue = maxMarksValue;\n      } else if (newValue <= marksValues[0]) {\n        newValue = marksValues[0];\n      } else {\n        newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n      }\n    }\n    newValue = clamp(newValue, min, max);\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index;\n\n      // Potentially swap the index if needed.\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, lastChangedValue.current ?? newValue);\n    }\n  };\n  const createHandleHiddenInputKeyDown = otherHandlers => event => {\n    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'PageUp', 'PageDown', 'Home', 'End'].includes(event.key)) {\n      event.preventDefault();\n      const index = Number(event.currentTarget.getAttribute('data-index'));\n      const value = values[index];\n      let newValue = null;\n      // Keys actions that change the value by more than the most granular `step`\n      // value are only applied if the step not `null`.\n      // When step is `null`, the `marks` prop is used instead to define valid values.\n      if (step != null) {\n        const stepSize = event.shiftKey ? shiftStep : step;\n        switch (event.key) {\n          case 'ArrowUp':\n            newValue = getNewValue(value, stepSize, 1, min, max);\n            break;\n          case 'ArrowRight':\n            newValue = getNewValue(value, stepSize, isRtl ? -1 : 1, min, max);\n            break;\n          case 'ArrowDown':\n            newValue = getNewValue(value, stepSize, -1, min, max);\n            break;\n          case 'ArrowLeft':\n            newValue = getNewValue(value, stepSize, isRtl ? 1 : -1, min, max);\n            break;\n          case 'PageUp':\n            newValue = getNewValue(value, shiftStep, 1, min, max);\n            break;\n          case 'PageDown':\n            newValue = getNewValue(value, shiftStep, -1, min, max);\n            break;\n          case 'Home':\n            newValue = min;\n            break;\n          case 'End':\n            newValue = max;\n            break;\n          default:\n            break;\n        }\n      } else if (marks) {\n        const maxMarksValue = marksValues[marksValues.length - 1];\n        const currentMarkIndex = marksValues.indexOf(value);\n        const decrementKeys = [isRtl ? 'ArrowRight' : 'ArrowLeft', 'ArrowDown', 'PageDown', 'Home'];\n        const incrementKeys = [isRtl ? 'ArrowLeft' : 'ArrowRight', 'ArrowUp', 'PageUp', 'End'];\n        if (decrementKeys.includes(event.key)) {\n          if (currentMarkIndex === 0) {\n            newValue = marksValues[0];\n          } else {\n            newValue = marksValues[currentMarkIndex - 1];\n          }\n        } else if (incrementKeys.includes(event.key)) {\n          if (currentMarkIndex === marksValues.length - 1) {\n            newValue = maxMarksValue;\n          } else {\n            newValue = marksValues[currentMarkIndex + 1];\n          }\n        }\n      }\n      if (newValue != null) {\n        changeValue(event, newValue);\n      }\n    }\n    otherHandlers?.onKeyDown?.(event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/p/sandbox/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      document.activeElement?.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    otherHandlers.onChange?.(event);\n    // this handles value change by Pointer or Touch events\n    // @ts-ignore\n    changeValue(event, event.target.valueAsNumber);\n  };\n  const previousIndex = React.useRef(undefined);\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.startsWith('vertical')) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.includes('-reverse')) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      }\n\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[activeIndex - 1] || -Infinity, values[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index: activeIndex\n      });\n\n      // Potentially swap the index if needed.\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1;\n\n    // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, lastChangedValue.current ?? newValue);\n    }\n    touchId.current = undefined;\n\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    }\n    // If touch-action: none; is not supported we need to prevent the scroll manually.\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('touchend', handleTouchEnd, {\n      passive: true\n    });\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      slider.removeEventListener('touchstart', handleTouchStart);\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    otherHandlers.onMouseDown?.(event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    // Only handle left clicks\n    if (event.button !== 0) {\n      return;\n    }\n\n    // Avoid text selection\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange && !areValuesEqual(newValue, valueDerived)) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove, {\n      passive: true\n    });\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      ...externalProps,\n      ref: handleRef,\n      ...mergedEventHandlers\n    };\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    otherHandlers.onMouseOver?.(event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    otherHandlers.onMouseLeave?.(event);\n    setOpen(-1);\n  };\n  const getThumbProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(externalHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(externalHandlers || {})\n    };\n    return {\n      ...externalProps,\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n  };\n  const getThumbStyle = index => {\n    return {\n      // So the non active thumb doesn't show its label on hover.\n      pointerEvents: active !== -1 && active !== index ? 'none' : undefined\n    };\n  };\n  let cssWritingMode;\n  if (orientation === 'vertical') {\n    cssWritingMode = isRtl ? 'vertical-rl' : 'vertical-lr';\n  }\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalHandlers = extractEventHandlers(externalProps);\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(externalHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(externalHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(externalHandlers || {}),\n      onKeyDown: createHandleHiddenInputKeyDown(externalHandlers || {})\n    };\n    const mergedEventHandlers = {\n      ...externalHandlers,\n      ...ownEventHandlers\n    };\n    return {\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: parameters.step === null && parameters.marks ? 'any' : parameters.step ?? undefined,\n      disabled,\n      ...externalProps,\n      ...mergedEventHandlers,\n      style: {\n        ...visuallyHidden,\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%',\n        writingMode: cssWritingMode\n      }\n    };\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    rootRef: handleRef,\n    trackLeap,\n    trackOffset,\n    values,\n    getThumbStyle\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,cAAc,MAAM,4BAA4B;AACvD,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,SAASC,WAAWA,CAACC,YAAY,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5D,OAAOF,SAAS,KAAK,CAAC,GAAGG,IAAI,CAACF,GAAG,CAACH,YAAY,GAAGC,IAAI,EAAEG,GAAG,CAAC,GAAGC,IAAI,CAACD,GAAG,CAACJ,YAAY,GAAGC,IAAI,EAAEE,GAAG,CAAC;AAClG;AACA,SAASG,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEV,YAAY,EAAE;EACzC,MAAM;IACJW,KAAK,EAAEC;EACT,CAAC,GAAGF,MAAM,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,EAAEJ,KAAK,KAAK;IACvC,MAAMK,QAAQ,GAAGX,IAAI,CAACY,GAAG,CAACjB,YAAY,GAAGe,KAAK,CAAC;IAC/C,IAAID,GAAG,KAAK,IAAI,IAAIE,QAAQ,GAAGF,GAAG,CAACE,QAAQ,IAAIA,QAAQ,KAAKF,GAAG,CAACE,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRL;MACF,CAAC;IACH;IACA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;EACd,OAAOF,YAAY;AACrB;AACA,SAASM,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IACxB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAC1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACA,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AACA,OAAO,SAASC,cAAcA,CAAClB,KAAK,EAAEZ,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACW,KAAK,GAAGZ,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AACA,SAAS+B,cAAcA,CAACC,OAAO,EAAEhC,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAIgC,OAAO,GAAGhC,GAAG;AACpC;AACA,SAASiC,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAIhC,IAAI,CAACY,GAAG,CAACoB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACf,MAAM,GAAG,CAAC,IAAIgB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EACA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACjB,MAAM,GAAG,CAAC;AAC7C;AACA,SAASmB,gBAAgBA,CAAC9B,KAAK,EAAEd,IAAI,EAAEE,GAAG,EAAE;EAC1C,MAAM2C,OAAO,GAAGzC,IAAI,CAAC0C,KAAK,CAAC,CAAChC,KAAK,GAAGZ,GAAG,IAAIF,IAAI,CAAC,GAAGA,IAAI,GAAGE,GAAG;EAC7D,OAAO6C,MAAM,CAACF,OAAO,CAACG,OAAO,CAACb,mBAAmB,CAACnC,IAAI,CAAC,CAAC,CAAC;AAC3D;AACA,SAASiD,aAAaA,CAAC;EACrBxC,MAAM;EACNyC,QAAQ;EACRxC;AACF,CAAC,EAAE;EACD,MAAMyC,MAAM,GAAG1C,MAAM,CAAC2C,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAACzC,KAAK,CAAC,GAAGwC,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAAChD,GAAG,CAAC;AACzB;AACA,SAASiD,UAAUA,CAAC;EAClBC,SAAS;EACTC,WAAW;EACXC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGvE,aAAa,CAACoE,SAAS,CAACnC,OAAO,CAAC;EAC5C,IAAI,CAACmC,SAAS,CAACnC,OAAO,EAAEuC,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,IAAIb,MAAM,CAACW,GAAG,EAAEE,aAAa,EAAEC,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKL,WAAW,EAAE;IAC7HD,SAAS,CAACnC,OAAO,EAAE0C,aAAa,CAAC,8BAA8BN,WAAW,IAAI,CAAC,CAACO,KAAK,CAAC,CAAC;EACzF;EACA,IAAIN,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AACA,SAASQ,cAAcA,CAACd,QAAQ,EAAEe,QAAQ,EAAE;EAC1C,IAAI,OAAOf,QAAQ,KAAK,QAAQ,IAAI,OAAOe,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOf,QAAQ,KAAKe,QAAQ;EAC9B;EACA,IAAI,OAAOf,QAAQ,KAAK,QAAQ,IAAI,OAAOe,QAAQ,KAAK,QAAQ,EAAE;IAChE,OAAOrE,cAAc,CAACsD,QAAQ,EAAEe,QAAQ,CAAC;EAC3C;EACA,OAAO,KAAK;AACd;AACA,MAAMC,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAElC,OAAO,KAAK;MAClBmC,IAAI,EAAE,GAAGnC,OAAO;IAClB,CAAC,CAAC;IACFoC,IAAI,EAAEpC,OAAO,KAAK;MAChBqC,KAAK,EAAE,GAAGrC,OAAO;IACnB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpBkC,MAAM,EAAElC,OAAO,KAAK;MAClBsC,KAAK,EAAE,GAAGtC,OAAO;IACnB,CAAC,CAAC;IACFoC,IAAI,EAAEpC,OAAO,KAAK;MAChBqC,KAAK,EAAE,GAAGrC,OAAO;IACnB,CAAC;EACH,CAAC;EACDuC,QAAQ,EAAE;IACRL,MAAM,EAAElC,OAAO,KAAK;MAClBwC,MAAM,EAAE,GAAGxC,OAAO;IACpB,CAAC,CAAC;IACFoC,IAAI,EAAEpC,OAAO,KAAK;MAChByC,MAAM,EAAE,GAAGzC,OAAO;IACpB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAM0C,QAAQ,GAAGhD,CAAC,IAAIA,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiD,6BAA6B;AACjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAKxD,SAAS,EAAE;IAC/C,IAAI,OAAO0D,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EACA,OAAOA,6BAA6B;AACtC;AACA,OAAO,SAASI,SAASA,CAACC,UAAU,EAAE;EACpC,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxBtF,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACPwF,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,OAAO,EAAEC,GAAG;IACZC,KAAK,GAAGpB,QAAQ;IAChB5E,IAAI,GAAG,CAAC;IACRiG,SAAS,GAAG,EAAE;IACdC,QAAQ;IACRpF,KAAK,EAAEqF;EACT,CAAC,GAAGjB,UAAU;EACd,MAAM/D,OAAO,GAAGjC,KAAK,CAACkH,MAAM,CAAC/E,SAAS,CAAC;EACvC;EACA;EACA;EACA,MAAM,CAACgF,MAAM,EAAE5C,SAAS,CAAC,GAAGvE,KAAK,CAACoH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtH,KAAK,CAACoH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGxH,KAAK,CAACoH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAGzH,KAAK,CAACkH,MAAM,CAAC,CAAC,CAAC;EACjC;EACA,MAAMQ,gBAAgB,GAAG1H,KAAK,CAACkH,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAM,CAACS,YAAY,EAAEC,aAAa,CAAC,GAAG1H,aAAa,CAAC;IAClD2H,UAAU,EAAEZ,SAAS;IACrBa,OAAO,EAAE5B,YAAY,IAAIlF,GAAG;IAC5BwF,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMuB,YAAY,GAAGtB,QAAQ,KAAK,CAACzE,KAAK,EAAEJ,KAAK,EAAEoG,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGjG,KAAK,CAACiG,WAAW,IAAIjG,KAAK;IAC9C;IACA,MAAMkG,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACd3G,KAAK,EAAE;QACLA,KAAK;QACL4E;MACF;IACF,CAAC,CAAC;IACFkB,gBAAgB,CAACxF,OAAO,GAAGN,KAAK;IAChC6E,QAAQ,CAACyB,WAAW,EAAEtG,KAAK,EAAEoG,UAAU,CAAC;EAC1C,CAAC,CAAC;EACF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAIpG,MAAM,GAAGiH,KAAK,GAAGb,YAAY,CAACzD,KAAK,CAAC,CAAC,CAACC,IAAI,CAAChD,GAAG,CAAC,GAAG,CAACwG,YAAY,CAAC;EACpEpG,MAAM,GAAGA,MAAM,CAACoH,GAAG,CAAC/G,KAAK,IAAIA,KAAK,IAAI,IAAI,GAAGZ,GAAG,GAAGR,KAAK,CAACoB,KAAK,EAAEZ,GAAG,EAAEC,GAAG,CAAC,CAAC;EAC1E,MAAMqF,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAIzF,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG2H,KAAK,CAACvH,IAAI,CAAC0H,KAAK,CAAC,CAAC3H,GAAG,GAAGD,GAAG,IAAIF,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC6H,GAAG,CAAC,CAACE,CAAC,EAAErH,KAAK,MAAM;IACpHI,KAAK,EAAEZ,GAAG,GAAGF,IAAI,GAAGU;EACtB,CAAC,CAAC,CAAC,GAAG+E,SAAS,IAAI,EAAE;EACrB,MAAMuC,WAAW,GAAGxC,KAAK,CAACqC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAACnH,KAAK,CAAC;EACjD,MAAM,CAACoH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjJ,KAAK,CAACoH,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM/C,SAAS,GAAGrE,KAAK,CAACkH,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMgC,SAAS,GAAG7I,UAAU,CAACwG,GAAG,EAAExC,SAAS,CAAC;EAC5C,MAAM8E,4BAA4B,GAAGC,aAAa,IAAIpH,KAAK,IAAI;IAC7D,MAAMR,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACqH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,IAAIrE,cAAc,CAAC0B,KAAK,CAACsH,MAAM,CAAC,EAAE;MAChCL,oBAAoB,CAACzH,KAAK,CAAC;IAC7B;IACA8F,OAAO,CAAC9F,KAAK,CAAC;IACd4H,aAAa,EAAEG,OAAO,GAAGvH,KAAK,CAAC;EACjC,CAAC;EACD,MAAMwH,2BAA2B,GAAGJ,aAAa,IAAIpH,KAAK,IAAI;IAC5D,IAAI,CAAC1B,cAAc,CAAC0B,KAAK,CAACsH,MAAM,CAAC,EAAE;MACjCL,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA3B,OAAO,CAAC,CAAC,CAAC,CAAC;IACX8B,aAAa,EAAEK,MAAM,GAAGzH,KAAK,CAAC;EAChC,CAAC;EACD,MAAM0H,WAAW,GAAGA,CAAC1H,KAAK,EAAE2H,UAAU,KAAK;IACzC,MAAMnI,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACqH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAM/C,KAAK,GAAGL,MAAM,CAACC,KAAK,CAAC;IAC3B,MAAMoI,UAAU,GAAGd,WAAW,CAACe,OAAO,CAACjI,KAAK,CAAC;IAC7C,IAAIoC,QAAQ,GAAG2F,UAAU;IACzB,IAAIrD,KAAK,IAAIxF,IAAI,IAAI,IAAI,EAAE;MACzB,MAAMgJ,aAAa,GAAGhB,WAAW,CAACA,WAAW,CAACvG,MAAM,GAAG,CAAC,CAAC;MACzD,IAAIyB,QAAQ,IAAI8F,aAAa,EAAE;QAC7B9F,QAAQ,GAAG8F,aAAa;MAC1B,CAAC,MAAM,IAAI9F,QAAQ,IAAI8E,WAAW,CAAC,CAAC,CAAC,EAAE;QACrC9E,QAAQ,GAAG8E,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACL9E,QAAQ,GAAGA,QAAQ,GAAGpC,KAAK,GAAGkH,WAAW,CAACc,UAAU,GAAG,CAAC,CAAC,GAAGd,WAAW,CAACc,UAAU,GAAG,CAAC,CAAC;MACzF;IACF;IACA5F,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAEhD,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIuH,KAAK,EAAE;MACT;MACA,IAAIpC,WAAW,EAAE;QACfpC,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAEzC,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,IAAI,CAACuI,QAAQ,EAAExI,MAAM,CAACC,KAAK,GAAG,CAAC,CAAC,IAAIuI,QAAQ,CAAC;MAC3F;MACA,MAAMC,aAAa,GAAGhG,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvBxC,MAAM;QACNyC,QAAQ;QACRxC;MACF,CAAC,CAAC;MACF,IAAI8C,WAAW,GAAG9C,KAAK;;MAEvB;MACA,IAAI,CAAC4E,WAAW,EAAE;QAChB9B,WAAW,GAAGN,QAAQ,CAAC6F,OAAO,CAACG,aAAa,CAAC;MAC/C;MACA5F,UAAU,CAAC;QACTC,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IACAsD,aAAa,CAAC5D,QAAQ,CAAC;IACvBiF,oBAAoB,CAACzH,KAAK,CAAC;IAC3B,IAAIuG,YAAY,IAAI,CAACjD,cAAc,CAACd,QAAQ,EAAE2D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAAC/F,KAAK,EAAEgC,QAAQ,EAAExC,KAAK,CAAC;IACtC;IACA,IAAIkF,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC1E,KAAK,EAAE0F,gBAAgB,CAACxF,OAAO,IAAI8B,QAAQ,CAAC;IAChE;EACF,CAAC;EACD,MAAMiG,8BAA8B,GAAGb,aAAa,IAAIpH,KAAK,IAAI;IAC/D,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,CAACkI,QAAQ,CAAClI,KAAK,CAACmI,GAAG,CAAC,EAAE;MAChHnI,KAAK,CAACoI,cAAc,CAAC,CAAC;MACtB,MAAM5I,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACqH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;MACpE,MAAM/C,KAAK,GAAGL,MAAM,CAACC,KAAK,CAAC;MAC3B,IAAIwC,QAAQ,GAAG,IAAI;MACnB;MACA;MACA;MACA,IAAIlD,IAAI,IAAI,IAAI,EAAE;QAChB,MAAMuJ,QAAQ,GAAGrI,KAAK,CAACsI,QAAQ,GAAGvD,SAAS,GAAGjG,IAAI;QAClD,QAAQkB,KAAK,CAACmI,GAAG;UACf,KAAK,SAAS;YACZnG,QAAQ,GAAGpD,WAAW,CAACgB,KAAK,EAAEyI,QAAQ,EAAE,CAAC,EAAErJ,GAAG,EAAEC,GAAG,CAAC;YACpD;UACF,KAAK,YAAY;YACf+C,QAAQ,GAAGpD,WAAW,CAACgB,KAAK,EAAEyI,QAAQ,EAAEhE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,EAAErF,GAAG,EAAEC,GAAG,CAAC;YACjE;UACF,KAAK,WAAW;YACd+C,QAAQ,GAAGpD,WAAW,CAACgB,KAAK,EAAEyI,QAAQ,EAAE,CAAC,CAAC,EAAErJ,GAAG,EAAEC,GAAG,CAAC;YACrD;UACF,KAAK,WAAW;YACd+C,QAAQ,GAAGpD,WAAW,CAACgB,KAAK,EAAEyI,QAAQ,EAAEhE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAErF,GAAG,EAAEC,GAAG,CAAC;YACjE;UACF,KAAK,QAAQ;YACX+C,QAAQ,GAAGpD,WAAW,CAACgB,KAAK,EAAEmF,SAAS,EAAE,CAAC,EAAE/F,GAAG,EAAEC,GAAG,CAAC;YACrD;UACF,KAAK,UAAU;YACb+C,QAAQ,GAAGpD,WAAW,CAACgB,KAAK,EAAEmF,SAAS,EAAE,CAAC,CAAC,EAAE/F,GAAG,EAAEC,GAAG,CAAC;YACtD;UACF,KAAK,MAAM;YACT+C,QAAQ,GAAGhD,GAAG;YACd;UACF,KAAK,KAAK;YACRgD,QAAQ,GAAG/C,GAAG;YACd;UACF;YACE;QACJ;MACF,CAAC,MAAM,IAAIqF,KAAK,EAAE;QAChB,MAAMwD,aAAa,GAAGhB,WAAW,CAACA,WAAW,CAACvG,MAAM,GAAG,CAAC,CAAC;QACzD,MAAMgI,gBAAgB,GAAGzB,WAAW,CAACe,OAAO,CAACjI,KAAK,CAAC;QACnD,MAAM4I,aAAa,GAAG,CAACnE,KAAK,GAAG,YAAY,GAAG,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC;QAC3F,MAAMoE,aAAa,GAAG,CAACpE,KAAK,GAAG,WAAW,GAAG,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC;QACtF,IAAImE,aAAa,CAACN,QAAQ,CAAClI,KAAK,CAACmI,GAAG,CAAC,EAAE;UACrC,IAAII,gBAAgB,KAAK,CAAC,EAAE;YAC1BvG,QAAQ,GAAG8E,WAAW,CAAC,CAAC,CAAC;UAC3B,CAAC,MAAM;YACL9E,QAAQ,GAAG8E,WAAW,CAACyB,gBAAgB,GAAG,CAAC,CAAC;UAC9C;QACF,CAAC,MAAM,IAAIE,aAAa,CAACP,QAAQ,CAAClI,KAAK,CAACmI,GAAG,CAAC,EAAE;UAC5C,IAAII,gBAAgB,KAAKzB,WAAW,CAACvG,MAAM,GAAG,CAAC,EAAE;YAC/CyB,QAAQ,GAAG8F,aAAa;UAC1B,CAAC,MAAM;YACL9F,QAAQ,GAAG8E,WAAW,CAACyB,gBAAgB,GAAG,CAAC,CAAC;UAC9C;QACF;MACF;MACA,IAAIvG,QAAQ,IAAI,IAAI,EAAE;QACpB0F,WAAW,CAAC1H,KAAK,EAAEgC,QAAQ,CAAC;MAC9B;IACF;IACAoF,aAAa,EAAEsB,SAAS,GAAG1I,KAAK,CAAC;EACnC,CAAC;EACD7B,iBAAiB,CAAC,MAAM;IACtB,IAAIgG,QAAQ,IAAI9B,SAAS,CAACnC,OAAO,CAACuC,QAAQ,CAACkG,QAAQ,CAACjG,aAAa,CAAC,EAAE;MAClE;MACA;MACA;MACA;MACAiG,QAAQ,CAACjG,aAAa,EAAEkG,IAAI,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACzE,QAAQ,CAAC,CAAC;EACd,IAAIA,QAAQ,IAAIgB,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7B5C,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EACA,IAAI4B,QAAQ,IAAI6C,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EACA,MAAM4B,6BAA6B,GAAGzB,aAAa,IAAIpH,KAAK,IAAI;IAC9DoH,aAAa,CAAC3C,QAAQ,GAAGzE,KAAK,CAAC;IAC/B;IACA;IACA0H,WAAW,CAAC1H,KAAK,EAAEA,KAAK,CAACsH,MAAM,CAACwB,aAAa,CAAC;EAChD,CAAC;EACD,MAAMC,aAAa,GAAG/K,KAAK,CAACkH,MAAM,CAAC/E,SAAS,CAAC;EAC7C,IAAI6I,IAAI,GAAGrE,WAAW;EACtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzCqE,IAAI,IAAI,UAAU;EACpB;EACA,MAAMC,iBAAiB,GAAGA,CAAC;IACzBC,MAAM;IACNC,IAAI,GAAG;EACT,CAAC,KAAK;IACJ,MAAM;MACJjJ,OAAO,EAAEkJ;IACX,CAAC,GAAG/G,SAAS;IACb,MAAM;MACJgB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAGiG,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAIrI,OAAO;IACX,IAAIgI,IAAI,CAACM,UAAU,CAAC,UAAU,CAAC,EAAE;MAC/BtI,OAAO,GAAG,CAACwC,MAAM,GAAG0F,MAAM,CAACtI,CAAC,IAAI6C,MAAM;IACxC,CAAC,MAAM;MACLzC,OAAO,GAAG,CAACkI,MAAM,CAACxI,CAAC,GAAGyC,IAAI,IAAIE,KAAK;IACrC;IACA,IAAI2F,IAAI,CAACd,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7BlH,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IACA,IAAIgB,QAAQ;IACZA,QAAQ,GAAGjB,cAAc,CAACC,OAAO,EAAEhC,GAAG,EAAEC,GAAG,CAAC;IAC5C,IAAIH,IAAI,EAAE;MACRkD,QAAQ,GAAGN,gBAAgB,CAACM,QAAQ,EAAElD,IAAI,EAAEE,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMS,YAAY,GAAGH,WAAW,CAACwH,WAAW,EAAE9E,QAAQ,CAAC;MACvDA,QAAQ,GAAG8E,WAAW,CAACrH,YAAY,CAAC;IACtC;IACAuC,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAEhD,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIqD,WAAW,GAAG,CAAC;IACnB,IAAIkE,KAAK,EAAE;MACT,IAAI,CAAC2C,IAAI,EAAE;QACT7G,WAAW,GAAGhD,WAAW,CAACC,MAAM,EAAEyC,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACLM,WAAW,GAAGyG,aAAa,CAAC7I,OAAO;MACrC;;MAEA;MACA,IAAIkE,WAAW,EAAE;QACfpC,QAAQ,GAAGxD,KAAK,CAACwD,QAAQ,EAAEzC,MAAM,CAAC+C,WAAW,GAAG,CAAC,CAAC,IAAI,CAACyF,QAAQ,EAAExI,MAAM,CAAC+C,WAAW,GAAG,CAAC,CAAC,IAAIyF,QAAQ,CAAC;MACvG;MACA,MAAMC,aAAa,GAAGhG,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvBxC,MAAM;QACNyC,QAAQ;QACRxC,KAAK,EAAE8C;MACT,CAAC,CAAC;;MAEF;MACA,IAAI,EAAE8B,WAAW,IAAI+E,IAAI,CAAC,EAAE;QAC1B7G,WAAW,GAAGN,QAAQ,CAAC6F,OAAO,CAACG,aAAa,CAAC;QAC7Ce,aAAa,CAAC7I,OAAO,GAAGoC,WAAW;MACrC;IACF;IACA,OAAO;MACLN,QAAQ;MACRM;IACF,CAAC;EACH,CAAC;EACD,MAAMiH,eAAe,GAAGnL,gBAAgB,CAAC6H,WAAW,IAAI;IACtD,MAAMiD,MAAM,GAAGnJ,WAAW,CAACkG,WAAW,EAAEhG,OAAO,CAAC;IAChD,IAAI,CAACiJ,MAAM,EAAE;MACX;IACF;IACAzD,SAAS,CAACvF,OAAO,IAAI,CAAC;;IAEtB;IACA;IACA,IAAI+F,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAACuD,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAACxD,WAAW,CAAC;MAC3B;IACF;IACA,MAAM;MACJjE,QAAQ;MACRM;IACF,CAAC,GAAG2G,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF/G,UAAU,CAAC;MACTC,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFqD,aAAa,CAAC5D,QAAQ,CAAC;IACvB,IAAI,CAACuD,QAAQ,IAAIE,SAAS,CAACvF,OAAO,GAAGvB,gCAAgC,EAAE;MACrE6G,WAAW,CAAC,IAAI,CAAC;IACnB;IACA,IAAIO,YAAY,IAAI,CAACjD,cAAc,CAACd,QAAQ,EAAE2D,YAAY,CAAC,EAAE;MAC3DI,YAAY,CAACE,WAAW,EAAEjE,QAAQ,EAAEM,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAMmH,cAAc,GAAGrL,gBAAgB,CAAC6H,WAAW,IAAI;IACrD,MAAMiD,MAAM,GAAGnJ,WAAW,CAACkG,WAAW,EAAEhG,OAAO,CAAC;IAChDuF,WAAW,CAAC,KAAK,CAAC;IAClB,IAAI,CAAC0D,MAAM,EAAE;MACX;IACF;IACA,MAAM;MACJlH;IACF,CAAC,GAAGiH,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE;IACR,CAAC,CAAC;IACF5G,SAAS,CAAC,CAAC,CAAC,CAAC;IACb,IAAI0D,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCd,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IACA,IAAIZ,iBAAiB,EAAE;MACrBA,iBAAiB,CAACuB,WAAW,EAAEP,gBAAgB,CAACxF,OAAO,IAAI8B,QAAQ,CAAC;IACtE;IACA/B,OAAO,CAACC,OAAO,GAAGC,SAAS;;IAE3B;IACAuJ,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGvL,gBAAgB,CAAC6H,WAAW,IAAI;IACvD,IAAI9B,QAAQ,EAAE;MACZ;IACF;IACA;IACA,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCqC,WAAW,CAACmC,cAAc,CAAC,CAAC;IAC9B;IACA,MAAM5H,KAAK,GAAGyF,WAAW,CAAC7F,cAAc,CAAC,CAAC,CAAC;IAC3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IACA,MAAMyI,MAAM,GAAGnJ,WAAW,CAACkG,WAAW,EAAEhG,OAAO,CAAC;IAChD,IAAIiJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJlH,QAAQ;QACRM;MACF,CAAC,GAAG2G,iBAAiB,CAAC;QACpBC;MACF,CAAC,CAAC;MACF9G,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFqD,aAAa,CAAC5D,QAAQ,CAAC;MACvB,IAAI+D,YAAY,IAAI,CAACjD,cAAc,CAACd,QAAQ,EAAE2D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAACE,WAAW,EAAEjE,QAAQ,EAAEM,WAAW,CAAC;MAClD;IACF;IACAmD,SAAS,CAACvF,OAAO,GAAG,CAAC;IACrB,MAAMsC,GAAG,GAAGvE,aAAa,CAACoE,SAAS,CAACnC,OAAO,CAAC;IAC5CsC,GAAG,CAACoH,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACFrH,GAAG,CAACoH,gBAAgB,CAAC,UAAU,EAAEH,cAAc,EAAE;MAC/CI,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMH,aAAa,GAAG1L,KAAK,CAAC8L,WAAW,CAAC,MAAM;IAC5C,MAAMtH,GAAG,GAAGvE,aAAa,CAACoE,SAAS,CAACnC,OAAO,CAAC;IAC5CsC,GAAG,CAACuH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrD/G,GAAG,CAACuH,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClDjH,GAAG,CAACuH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrD/G,GAAG,CAACuH,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrCvL,KAAK,CAACgM,SAAS,CAAC,MAAM;IACpB,MAAM;MACJ9J,OAAO,EAAEkJ;IACX,CAAC,GAAG/G,SAAS;IACb+G,MAAM,CAACQ,gBAAgB,CAAC,YAAY,EAAED,gBAAgB,EAAE;MACtDE,OAAO,EAAEjG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACXwF,MAAM,CAACW,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,CAAC;MAC1DD,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrC3L,KAAK,CAACgM,SAAS,CAAC,MAAM;IACpB,IAAI7F,QAAQ,EAAE;MACZuF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACvF,QAAQ,EAAEuF,aAAa,CAAC,CAAC;EAC7B,MAAMO,qBAAqB,GAAG7C,aAAa,IAAIpH,KAAK,IAAI;IACtDoH,aAAa,CAAC8C,WAAW,GAAGlK,KAAK,CAAC;IAClC,IAAImE,QAAQ,EAAE;MACZ;IACF;IACA,IAAInE,KAAK,CAACmK,gBAAgB,EAAE;MAC1B;IACF;;IAEA;IACA,IAAInK,KAAK,CAACoK,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;;IAEA;IACApK,KAAK,CAACoI,cAAc,CAAC,CAAC;IACtB,MAAMc,MAAM,GAAGnJ,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAC1C,IAAIiJ,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJlH,QAAQ;QACRM;MACF,CAAC,GAAG2G,iBAAiB,CAAC;QACpBC;MACF,CAAC,CAAC;MACF9G,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFqD,aAAa,CAAC5D,QAAQ,CAAC;MACvB,IAAI+D,YAAY,IAAI,CAACjD,cAAc,CAACd,QAAQ,EAAE2D,YAAY,CAAC,EAAE;QAC3DI,YAAY,CAAC/F,KAAK,EAAEgC,QAAQ,EAAEM,WAAW,CAAC;MAC5C;IACF;IACAmD,SAAS,CAACvF,OAAO,GAAG,CAAC;IACrB,MAAMsC,GAAG,GAAGvE,aAAa,CAACoE,SAAS,CAACnC,OAAO,CAAC;IAC5CsC,GAAG,CAACoH,gBAAgB,CAAC,WAAW,EAAEL,eAAe,EAAE;MACjDM,OAAO,EAAE;IACX,CAAC,CAAC;IACFrH,GAAG,CAACoH,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;EACjD,CAAC;EACD,MAAMY,WAAW,GAAGvJ,cAAc,CAAC0F,KAAK,GAAGjH,MAAM,CAAC,CAAC,CAAC,GAAGP,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMqL,SAAS,GAAGxJ,cAAc,CAACvB,MAAM,CAACA,MAAM,CAACgB,MAAM,GAAG,CAAC,CAAC,EAAEvB,GAAG,EAAEC,GAAG,CAAC,GAAGoL,WAAW;EACnF,MAAME,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMC,gBAAgB,GAAGhM,oBAAoB,CAAC+L,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBR,WAAW,EAAED,qBAAqB,CAACQ,gBAAgB,IAAI,CAAC,CAAC;IAC3D,CAAC;IACD,MAAME,mBAAmB,GAAG;MAC1B,GAAGF,gBAAgB;MACnB,GAAGC;IACL,CAAC;IACD,OAAO;MACL,GAAGF,aAAa;MAChB3F,GAAG,EAAEqC,SAAS;MACd,GAAGyD;IACL,CAAC;EACH,CAAC;EACD,MAAMC,qBAAqB,GAAGxD,aAAa,IAAIpH,KAAK,IAAI;IACtDoH,aAAa,CAACyD,WAAW,GAAG7K,KAAK,CAAC;IAClC,MAAMR,KAAK,GAAGqC,MAAM,CAAC7B,KAAK,CAACqH,aAAa,CAAC1E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE2C,OAAO,CAAC9F,KAAK,CAAC;EAChB,CAAC;EACD,MAAMsL,sBAAsB,GAAG1D,aAAa,IAAIpH,KAAK,IAAI;IACvDoH,aAAa,CAAC2D,YAAY,GAAG/K,KAAK,CAAC;IACnCsF,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EACD,MAAM0F,aAAa,GAAGA,CAACR,aAAa,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAMC,gBAAgB,GAAGhM,oBAAoB,CAAC+L,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBG,WAAW,EAAED,qBAAqB,CAACH,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC1DM,YAAY,EAAED,sBAAsB,CAACL,gBAAgB,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO;MACL,GAAGD,aAAa;MAChB,GAAGC,gBAAgB;MACnB,GAAGC;IACL,CAAC;EACH,CAAC;EACD,MAAMO,aAAa,GAAGzL,KAAK,IAAI;IAC7B,OAAO;MACL;MACA0L,aAAa,EAAE/F,MAAM,KAAK,CAAC,CAAC,IAAIA,MAAM,KAAK3F,KAAK,GAAG,MAAM,GAAGW;IAC9D,CAAC;EACH,CAAC;EACD,IAAIgL,cAAc;EAClB,IAAIxG,WAAW,KAAK,UAAU,EAAE;IAC9BwG,cAAc,GAAG9G,KAAK,GAAG,aAAa,GAAG,aAAa;EACxD;EACA,MAAM+G,mBAAmB,GAAGA,CAACZ,aAAa,GAAG,CAAC,CAAC,KAAK;IAClD,MAAMC,gBAAgB,GAAGhM,oBAAoB,CAAC+L,aAAa,CAAC;IAC5D,MAAME,gBAAgB,GAAG;MACvBjG,QAAQ,EAAEoE,6BAA6B,CAAC4B,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC/DlD,OAAO,EAAEJ,4BAA4B,CAACsD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC7DhD,MAAM,EAAED,2BAA2B,CAACiD,gBAAgB,IAAI,CAAC,CAAC,CAAC;MAC3D/B,SAAS,EAAET,8BAA8B,CAACwC,gBAAgB,IAAI,CAAC,CAAC;IAClE,CAAC;IACD,MAAME,mBAAmB,GAAG;MAC1B,GAAGF,gBAAgB;MACnB,GAAGC;IACL,CAAC;IACD,OAAO;MACL1F,QAAQ;MACR,iBAAiB,EAAEf,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEG,KAAK,CAAC7F,GAAG,CAAC;MAC3B,eAAe,EAAE6F,KAAK,CAAC9F,GAAG,CAAC;MAC3BwF,IAAI;MACJ4B,IAAI,EAAE,OAAO;MACbpH,GAAG,EAAEgF,UAAU,CAAChF,GAAG;MACnBC,GAAG,EAAE+E,UAAU,CAAC/E,GAAG;MACnBH,IAAI,EAAEkF,UAAU,CAAClF,IAAI,KAAK,IAAI,IAAIkF,UAAU,CAACM,KAAK,GAAG,KAAK,GAAGN,UAAU,CAAClF,IAAI,IAAIqB,SAAS;MACzFgE,QAAQ;MACR,GAAGqG,aAAa;MAChB,GAAGG,mBAAmB;MACtBU,KAAK,EAAE;QACL,GAAG9M,cAAc;QACjBQ,SAAS,EAAEsF,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE,MAAM;QACd6H,WAAW,EAAEH;MACf;IACF,CAAC;EACH,CAAC;EACD,OAAO;IACLhG,MAAM;IACN6D,IAAI,EAAEA,IAAI;IACVhG,SAAS;IACTuC,QAAQ;IACRyB,iBAAiB;IACjBoE,mBAAmB;IACnBb,YAAY;IACZS,aAAa;IACb1G,KAAK,EAAEA,KAAK;IACZe,IAAI;IACJmB,KAAK;IACL5B,OAAO,EAAEsC,SAAS;IAClBoD,SAAS;IACTD,WAAW;IACX9K,MAAM;IACN0L;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}