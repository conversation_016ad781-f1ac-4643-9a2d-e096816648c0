{"ast": null, "code": "import capitalize from '@mui/utils/capitalize';\nimport merge from \"../merge/index.js\";\nimport { getPath, getStyleValue as getValue } from \"../style/index.js\";\nimport { handleBreakpoints, createEmptyBreakpointObject, removeUnusedBreakpoints } from \"../breakpoints/index.js\";\nimport { sortContainerQueries } from \"../cssContainerQueries/index.js\";\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nfunction objectsHaveSameKeys(...objects) {\n  const allKeys = objects.reduce((keys, object) => keys.concat(Object.keys(object)), []);\n  const union = new Set(allKeys);\n  return objects.every(object => union.size === Object.keys(object).length);\n}\nfunction callIfFn(maybeFn, arg) {\n  return typeof maybeFn === 'function' ? maybeFn(arg) : maybeFn;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createStyleFunctionSx() {\n  function getThemeValue(prop, val, theme, config) {\n    const props = {\n      [prop]: val,\n      theme\n    };\n    const options = config[prop];\n    if (!options) {\n      return {\n        [prop]: val\n      };\n    }\n    const {\n      cssProperty = prop,\n      themeKey,\n      transform,\n      style\n    } = options;\n    if (val == null) {\n      return null;\n    }\n\n    // TODO v6: remove, see https://github.com/mui/material-ui/pull/38123\n    if (themeKey === 'typography' && val === 'inherit') {\n      return {\n        [prop]: val\n      };\n    }\n    const themeMapping = getPath(theme, themeKey) || {};\n    if (style) {\n      return style(props);\n    }\n    const styleFromPropValue = propValueFinal => {\n      let value = getValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, val, styleFromPropValue);\n  }\n  function styleFunctionSx(props) {\n    const {\n      sx,\n      theme = {}\n    } = props || {};\n    if (!sx) {\n      return null; // Emotion & styled-components will neglect null\n    }\n    const config = theme.unstable_sxConfig ?? defaultSxConfig;\n\n    /*\n     * Receive `sxInput` as object or callback\n     * and then recursively check keys & values to create media query object styles.\n     * (the result will be used in `styled`)\n     */\n    function traverse(sxInput) {\n      let sxObject = sxInput;\n      if (typeof sxInput === 'function') {\n        sxObject = sxInput(theme);\n      } else if (typeof sxInput !== 'object') {\n        // value\n        return sxInput;\n      }\n      if (!sxObject) {\n        return null;\n      }\n      const emptyBreakpoints = createEmptyBreakpointObject(theme.breakpoints);\n      const breakpointsKeys = Object.keys(emptyBreakpoints);\n      let css = emptyBreakpoints;\n      Object.keys(sxObject).forEach(styleKey => {\n        const value = callIfFn(sxObject[styleKey], theme);\n        if (value !== null && value !== undefined) {\n          if (typeof value === 'object') {\n            if (config[styleKey]) {\n              css = merge(css, getThemeValue(styleKey, value, theme, config));\n            } else {\n              const breakpointsValues = handleBreakpoints({\n                theme\n              }, value, x => ({\n                [styleKey]: x\n              }));\n              if (objectsHaveSameKeys(breakpointsValues, value)) {\n                css[styleKey] = styleFunctionSx({\n                  sx: value,\n                  theme\n                });\n              } else {\n                css = merge(css, breakpointsValues);\n              }\n            }\n          } else {\n            css = merge(css, getThemeValue(styleKey, value, theme, config));\n          }\n        }\n      });\n      return sortContainerQueries(theme, removeUnusedBreakpoints(breakpointsKeys, css));\n    }\n    return Array.isArray(sx) ? sx.map(traverse) : traverse(sx);\n  }\n  return styleFunctionSx;\n}\nconst styleFunctionSx = unstable_createStyleFunctionSx();\nstyleFunctionSx.filterProps = ['sx'];\nexport default styleFunctionSx;", "map": {"version": 3, "names": ["capitalize", "merge", "<PERSON><PERSON><PERSON>", "getStyleValue", "getValue", "handleBreakpoints", "createEmptyBreakpointObject", "removeUnusedBreakpoints", "sortContainerQueries", "defaultSxConfig", "objectsHaveSameKeys", "objects", "allKeys", "reduce", "keys", "object", "concat", "Object", "union", "Set", "every", "size", "length", "callIfFn", "maybeFn", "arg", "unstable_createStyleFunctionSx", "getThemeValue", "prop", "val", "theme", "config", "props", "options", "cssProperty", "<PERSON><PERSON><PERSON>", "transform", "style", "themeMapping", "styleFromPropValue", "propValueFinal", "value", "styleFunctionSx", "sx", "unstable_sxConfig", "traverse", "sxInput", "sxObject", "emptyBreakpoints", "breakpoints", "breakpointsKeys", "css", "for<PERSON>ach", "styleKey", "undefined", "breakpointsValues", "x", "Array", "isArray", "map", "filterProps"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/styleFunctionSx/styleFunctionSx.js"], "sourcesContent": ["import capitalize from '@mui/utils/capitalize';\nimport merge from \"../merge/index.js\";\nimport { getPath, getStyleValue as getValue } from \"../style/index.js\";\nimport { handleBreakpoints, createEmptyBreakpointObject, removeUnusedBreakpoints } from \"../breakpoints/index.js\";\nimport { sortContainerQueries } from \"../cssContainerQueries/index.js\";\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nfunction objectsHaveSameKeys(...objects) {\n  const allKeys = objects.reduce((keys, object) => keys.concat(Object.keys(object)), []);\n  const union = new Set(allKeys);\n  return objects.every(object => union.size === Object.keys(object).length);\n}\nfunction callIfFn(maybeFn, arg) {\n  return typeof maybeFn === 'function' ? maybeFn(arg) : maybeFn;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createStyleFunctionSx() {\n  function getThemeValue(prop, val, theme, config) {\n    const props = {\n      [prop]: val,\n      theme\n    };\n    const options = config[prop];\n    if (!options) {\n      return {\n        [prop]: val\n      };\n    }\n    const {\n      cssProperty = prop,\n      themeKey,\n      transform,\n      style\n    } = options;\n    if (val == null) {\n      return null;\n    }\n\n    // TODO v6: remove, see https://github.com/mui/material-ui/pull/38123\n    if (themeKey === 'typography' && val === 'inherit') {\n      return {\n        [prop]: val\n      };\n    }\n    const themeMapping = getPath(theme, themeKey) || {};\n    if (style) {\n      return style(props);\n    }\n    const styleFromPropValue = propValueFinal => {\n      let value = getValue(themeMapping, transform, propValueFinal);\n      if (propValueFinal === value && typeof propValueFinal === 'string') {\n        // Haven't found value\n        value = getValue(themeMapping, transform, `${prop}${propValueFinal === 'default' ? '' : capitalize(propValueFinal)}`, propValueFinal);\n      }\n      if (cssProperty === false) {\n        return value;\n      }\n      return {\n        [cssProperty]: value\n      };\n    };\n    return handleBreakpoints(props, val, styleFromPropValue);\n  }\n  function styleFunctionSx(props) {\n    const {\n      sx,\n      theme = {}\n    } = props || {};\n    if (!sx) {\n      return null; // Emotion & styled-components will neglect null\n    }\n    const config = theme.unstable_sxConfig ?? defaultSxConfig;\n\n    /*\n     * Receive `sxInput` as object or callback\n     * and then recursively check keys & values to create media query object styles.\n     * (the result will be used in `styled`)\n     */\n    function traverse(sxInput) {\n      let sxObject = sxInput;\n      if (typeof sxInput === 'function') {\n        sxObject = sxInput(theme);\n      } else if (typeof sxInput !== 'object') {\n        // value\n        return sxInput;\n      }\n      if (!sxObject) {\n        return null;\n      }\n      const emptyBreakpoints = createEmptyBreakpointObject(theme.breakpoints);\n      const breakpointsKeys = Object.keys(emptyBreakpoints);\n      let css = emptyBreakpoints;\n      Object.keys(sxObject).forEach(styleKey => {\n        const value = callIfFn(sxObject[styleKey], theme);\n        if (value !== null && value !== undefined) {\n          if (typeof value === 'object') {\n            if (config[styleKey]) {\n              css = merge(css, getThemeValue(styleKey, value, theme, config));\n            } else {\n              const breakpointsValues = handleBreakpoints({\n                theme\n              }, value, x => ({\n                [styleKey]: x\n              }));\n              if (objectsHaveSameKeys(breakpointsValues, value)) {\n                css[styleKey] = styleFunctionSx({\n                  sx: value,\n                  theme\n                });\n              } else {\n                css = merge(css, breakpointsValues);\n              }\n            }\n          } else {\n            css = merge(css, getThemeValue(styleKey, value, theme, config));\n          }\n        }\n      });\n      return sortContainerQueries(theme, removeUnusedBreakpoints(breakpointsKeys, css));\n    }\n    return Array.isArray(sx) ? sx.map(traverse) : traverse(sx);\n  }\n  return styleFunctionSx;\n}\nconst styleFunctionSx = unstable_createStyleFunctionSx();\nstyleFunctionSx.filterProps = ['sx'];\nexport default styleFunctionSx;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,OAAO,EAAEC,aAAa,IAAIC,QAAQ,QAAQ,mBAAmB;AACtE,SAASC,iBAAiB,EAAEC,2BAA2B,EAAEC,uBAAuB,QAAQ,yBAAyB;AACjH,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,mBAAmBA,CAAC,GAAGC,OAAO,EAAE;EACvC,MAAMC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,MAAM,KAAKD,IAAI,CAACE,MAAM,CAACC,MAAM,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF,MAAMG,KAAK,GAAG,IAAIC,GAAG,CAACP,OAAO,CAAC;EAC9B,OAAOD,OAAO,CAACS,KAAK,CAACL,MAAM,IAAIG,KAAK,CAACG,IAAI,KAAKJ,MAAM,CAACH,IAAI,CAACC,MAAM,CAAC,CAACO,MAAM,CAAC;AAC3E;AACA,SAASC,QAAQA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC9B,OAAO,OAAOD,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACC,GAAG,CAAC,GAAGD,OAAO;AAC/D;;AAEA;AACA,OAAO,SAASE,8BAA8BA,CAAA,EAAG;EAC/C,SAASC,aAAaA,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC/C,MAAMC,KAAK,GAAG;MACZ,CAACJ,IAAI,GAAGC,GAAG;MACXC;IACF,CAAC;IACD,MAAMG,OAAO,GAAGF,MAAM,CAACH,IAAI,CAAC;IAC5B,IAAI,CAACK,OAAO,EAAE;MACZ,OAAO;QACL,CAACL,IAAI,GAAGC;MACV,CAAC;IACH;IACA,MAAM;MACJK,WAAW,GAAGN,IAAI;MAClBO,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGJ,OAAO;IACX,IAAIJ,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACb;;IAEA;IACA,IAAIM,QAAQ,KAAK,YAAY,IAAIN,GAAG,KAAK,SAAS,EAAE;MAClD,OAAO;QACL,CAACD,IAAI,GAAGC;MACV,CAAC;IACH;IACA,MAAMS,YAAY,GAAGpC,OAAO,CAAC4B,KAAK,EAAEK,QAAQ,CAAC,IAAI,CAAC,CAAC;IACnD,IAAIE,KAAK,EAAE;MACT,OAAOA,KAAK,CAACL,KAAK,CAAC;IACrB;IACA,MAAMO,kBAAkB,GAAGC,cAAc,IAAI;MAC3C,IAAIC,KAAK,GAAGrC,QAAQ,CAACkC,YAAY,EAAEF,SAAS,EAAEI,cAAc,CAAC;MAC7D,IAAIA,cAAc,KAAKC,KAAK,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;QAClE;QACAC,KAAK,GAAGrC,QAAQ,CAACkC,YAAY,EAAEF,SAAS,EAAE,GAAGR,IAAI,GAAGY,cAAc,KAAK,SAAS,GAAG,EAAE,GAAGxC,UAAU,CAACwC,cAAc,CAAC,EAAE,EAAEA,cAAc,CAAC;MACvI;MACA,IAAIN,WAAW,KAAK,KAAK,EAAE;QACzB,OAAOO,KAAK;MACd;MACA,OAAO;QACL,CAACP,WAAW,GAAGO;MACjB,CAAC;IACH,CAAC;IACD,OAAOpC,iBAAiB,CAAC2B,KAAK,EAAEH,GAAG,EAAEU,kBAAkB,CAAC;EAC1D;EACA,SAASG,eAAeA,CAACV,KAAK,EAAE;IAC9B,MAAM;MACJW,EAAE;MACFb,KAAK,GAAG,CAAC;IACX,CAAC,GAAGE,KAAK,IAAI,CAAC,CAAC;IACf,IAAI,CAACW,EAAE,EAAE;MACP,OAAO,IAAI,CAAC,CAAC;IACf;IACA,MAAMZ,MAAM,GAAGD,KAAK,CAACc,iBAAiB,IAAInC,eAAe;;IAEzD;AACJ;AACA;AACA;AACA;IACI,SAASoC,QAAQA,CAACC,OAAO,EAAE;MACzB,IAAIC,QAAQ,GAAGD,OAAO;MACtB,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;QACjCC,QAAQ,GAAGD,OAAO,CAAChB,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOgB,OAAO,KAAK,QAAQ,EAAE;QACtC;QACA,OAAOA,OAAO;MAChB;MACA,IAAI,CAACC,QAAQ,EAAE;QACb,OAAO,IAAI;MACb;MACA,MAAMC,gBAAgB,GAAG1C,2BAA2B,CAACwB,KAAK,CAACmB,WAAW,CAAC;MACvE,MAAMC,eAAe,GAAGjC,MAAM,CAACH,IAAI,CAACkC,gBAAgB,CAAC;MACrD,IAAIG,GAAG,GAAGH,gBAAgB;MAC1B/B,MAAM,CAACH,IAAI,CAACiC,QAAQ,CAAC,CAACK,OAAO,CAACC,QAAQ,IAAI;QACxC,MAAMZ,KAAK,GAAGlB,QAAQ,CAACwB,QAAQ,CAACM,QAAQ,CAAC,EAAEvB,KAAK,CAAC;QACjD,IAAIW,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKa,SAAS,EAAE;UACzC,IAAI,OAAOb,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAIV,MAAM,CAACsB,QAAQ,CAAC,EAAE;cACpBF,GAAG,GAAGlD,KAAK,CAACkD,GAAG,EAAExB,aAAa,CAAC0B,QAAQ,EAAEZ,KAAK,EAAEX,KAAK,EAAEC,MAAM,CAAC,CAAC;YACjE,CAAC,MAAM;cACL,MAAMwB,iBAAiB,GAAGlD,iBAAiB,CAAC;gBAC1CyB;cACF,CAAC,EAAEW,KAAK,EAAEe,CAAC,KAAK;gBACd,CAACH,QAAQ,GAAGG;cACd,CAAC,CAAC,CAAC;cACH,IAAI9C,mBAAmB,CAAC6C,iBAAiB,EAAEd,KAAK,CAAC,EAAE;gBACjDU,GAAG,CAACE,QAAQ,CAAC,GAAGX,eAAe,CAAC;kBAC9BC,EAAE,EAAEF,KAAK;kBACTX;gBACF,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLqB,GAAG,GAAGlD,KAAK,CAACkD,GAAG,EAAEI,iBAAiB,CAAC;cACrC;YACF;UACF,CAAC,MAAM;YACLJ,GAAG,GAAGlD,KAAK,CAACkD,GAAG,EAAExB,aAAa,CAAC0B,QAAQ,EAAEZ,KAAK,EAAEX,KAAK,EAAEC,MAAM,CAAC,CAAC;UACjE;QACF;MACF,CAAC,CAAC;MACF,OAAOvB,oBAAoB,CAACsB,KAAK,EAAEvB,uBAAuB,CAAC2C,eAAe,EAAEC,GAAG,CAAC,CAAC;IACnF;IACA,OAAOM,KAAK,CAACC,OAAO,CAACf,EAAE,CAAC,GAAGA,EAAE,CAACgB,GAAG,CAACd,QAAQ,CAAC,GAAGA,QAAQ,CAACF,EAAE,CAAC;EAC5D;EACA,OAAOD,eAAe;AACxB;AACA,MAAMA,eAAe,GAAGhB,8BAA8B,CAAC,CAAC;AACxDgB,eAAe,CAACkB,WAAW,GAAG,CAAC,IAAI,CAAC;AACpC,eAAelB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}