{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useThemeProps;\nvar _useThemeProps = _interopRequireDefault(require(\"@mui/system/useThemeProps\"));\nvar _defaultTheme = _interopRequireDefault(require(\"./defaultTheme\"));\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nfunction useThemeProps({\n  props,\n  name\n}) {\n  return (0, _useThemeProps.default)({\n    props,\n    name,\n    defaultTheme: _defaultTheme.default,\n    themeId: _identifier.default\n  });\n}", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "Object", "defineProperty", "exports", "value", "useThemeProps", "_useThemeProps", "_defaultTheme", "_identifier", "props", "name", "defaultTheme", "themeId"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/useThemeProps.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = useThemeProps;\nvar _useThemeProps = _interopRequireDefault(require(\"@mui/system/useThemeProps\"));\nvar _defaultTheme = _interopRequireDefault(require(\"./defaultTheme\"));\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nfunction useThemeProps({\n  props,\n  name\n}) {\n  return (0, _useThemeProps.default)({\n    props,\n    name,\n    defaultTheme: _defaultTheme.default,\n    themeId: _identifier.default\n  });\n}"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACH,OAAO,GAAGK,aAAa;AAC/B,IAAIC,cAAc,GAAGR,sBAAsB,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAAC;AACjF,IAAIQ,aAAa,GAAGT,sBAAsB,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC;AACrE,IAAIS,WAAW,GAAGV,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AACjE,SAASM,aAAaA,CAAC;EACrBI,KAAK;EACLC;AACF,CAAC,EAAE;EACD,OAAO,CAAC,CAAC,EAAEJ,cAAc,CAACN,OAAO,EAAE;IACjCS,KAAK;IACLC,IAAI;IACJC,YAAY,EAAEJ,aAAa,CAACP,OAAO;IACnCY,OAAO,EAAEJ,WAAW,CAACR;EACvB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}