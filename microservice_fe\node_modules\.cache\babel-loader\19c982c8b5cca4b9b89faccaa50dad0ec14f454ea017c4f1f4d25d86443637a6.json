{"ast": null, "code": "/**\n * WARNING: Don't import this directly. It's imported by the code generated by\n * `@mui/interal-babel-plugin-minify-errors`. Make sure to always use string literals in `Error`\n * constructors to ensure the plugin works as expected. Supported patterns include:\n *   throw new Error('My message');\n *   throw new Error(`My message: ${foo}`);\n *   throw new Error(`My message: ${foo}` + 'another string');\n *   ...\n * @param {number} code\n */\nexport default function formatMuiErrorMessage(code, ...args) {\n  const url = new URL(`https://mui.com/production-error/?code=${code}`);\n  args.forEach(arg => url.searchParams.append('args[]', arg));\n  return `Minified MUI error #${code}; visit ${url} for the full message.`;\n}", "map": {"version": 3, "names": ["formatMuiErrorMessage", "code", "args", "url", "URL", "for<PERSON>ach", "arg", "searchParams", "append"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/formatMuiErrorMessage/formatMuiErrorMessage.js"], "sourcesContent": ["/**\n * WARNING: Don't import this directly. It's imported by the code generated by\n * `@mui/interal-babel-plugin-minify-errors`. Make sure to always use string literals in `Error`\n * constructors to ensure the plugin works as expected. Supported patterns include:\n *   throw new Error('My message');\n *   throw new Error(`My message: ${foo}`);\n *   throw new Error(`My message: ${foo}` + 'another string');\n *   ...\n * @param {number} code\n */\nexport default function formatMuiErrorMessage(code, ...args) {\n  const url = new URL(`https://mui.com/production-error/?code=${code}`);\n  args.forEach(arg => url.searchParams.append('args[]', arg));\n  return `Minified MUI error #${code}; visit ${url} for the full message.`;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAACC,IAAI,EAAE,GAAGC,IAAI,EAAE;EAC3D,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,0CAA0CH,IAAI,EAAE,CAAC;EACrEC,IAAI,CAACG,OAAO,CAACC,GAAG,IAAIH,GAAG,CAACI,YAAY,CAACC,MAAM,CAAC,QAAQ,EAAEF,GAAG,CAAC,CAAC;EAC3D,OAAO,uBAAuBL,IAAI,WAAWE,GAAG,wBAAwB;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}