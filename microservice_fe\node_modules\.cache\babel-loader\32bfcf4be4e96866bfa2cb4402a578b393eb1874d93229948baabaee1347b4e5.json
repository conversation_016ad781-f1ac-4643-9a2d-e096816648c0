{"ast": null, "code": "import useEventCallback from '@mui/utils/useEventCallback';\nimport useTimeout from '@mui/utils/useTimeout';\nimport { useFieldRootHandleKeyDown } from \"./useFieldRootHandleKeyDown.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { syncSelectionToDOM } from \"./syncSelectionToDOM.js\";\n\n/**\n * Generate the props to pass to the root element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the root element of the field.\n */\nexport function useFieldRootProps(parameters) {\n  const {\n    manager,\n    focused,\n    setFocused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    internalPropsWithDefaults,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionOrder,\n      state,\n      // Methods to update the states\n      clearValue,\n      setCharacterQuery,\n      setSelectedSections,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n\n  // TODO: Inline onContainerKeyDown once the old DOM structure is removed\n  const handleKeyDown = useFieldRootHandleKeyDown({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const containerClickTimeout = useTimeout();\n  const handleClick = useEventCallback(event => {\n    if (disabled || !domGetters.isReady()) {\n      return;\n    }\n    setFocused(true);\n    if (parsedSelectedSections === 'all') {\n      containerClickTimeout.start(0, () => {\n        const cursorPosition = document.getSelection().getRangeAt(0).startOffset;\n        if (cursorPosition === 0) {\n          setSelectedSections(sectionOrder.startIndex);\n          return;\n        }\n        let sectionIndex = 0;\n        let cursorOnStartOfSection = 0;\n        while (cursorOnStartOfSection < cursorPosition && sectionIndex < state.sections.length) {\n          const section = state.sections[sectionIndex];\n          sectionIndex += 1;\n          cursorOnStartOfSection += `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`.length;\n        }\n        setSelectedSections(sectionIndex - 1);\n      });\n    } else if (!focused) {\n      setFocused(true);\n      setSelectedSections(sectionOrder.startIndex);\n    } else {\n      const hasClickedOnASection = domGetters.getRoot().contains(event.target);\n      if (!hasClickedOnASection) {\n        setSelectedSections(sectionOrder.startIndex);\n      }\n    }\n  });\n  const handleInput = useEventCallback(event => {\n    if (!domGetters.isReady() || parsedSelectedSections !== 'all') {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    domGetters.getRoot().innerHTML = state.sections.map(section => `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`).join('');\n    syncSelectionToDOM({\n      focused,\n      domGetters,\n      stateResponse\n    });\n    if (keyPressed.length === 0 || keyPressed.charCodeAt(0) === 10) {\n      clearValue();\n      setSelectedSections('all');\n    } else if (keyPressed.length > 1) {\n      updateValueFromValueStr(keyPressed);\n    } else {\n      if (parsedSelectedSections === 'all') {\n        setSelectedSections(0);\n      }\n      applyCharacterEditing({\n        keyPressed,\n        sectionIndex: 0\n      });\n    }\n  });\n  const handlePaste = useEventCallback(event => {\n    if (readOnly || parsedSelectedSections !== 'all') {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    event.preventDefault();\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleFocus = useEventCallback(() => {\n    if (focused || disabled || !domGetters.isReady()) {\n      return;\n    }\n    const activeElement = getActiveElement(document);\n    setFocused(true);\n    const isFocusInsideASection = domGetters.getSectionIndexFromDOMElement(activeElement) != null;\n    if (!isFocusInsideASection) {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleBlur = useEventCallback(() => {\n    setTimeout(() => {\n      if (!domGetters.isReady()) {\n        return;\n      }\n      const activeElement = getActiveElement(document);\n      const shouldBlur = !domGetters.getRoot().contains(activeElement);\n      if (shouldBlur) {\n        setFocused(false);\n        setSelectedSections(null);\n      }\n    });\n  });\n  return {\n    // Event handlers\n    onKeyDown: handleKeyDown,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    onClick: handleClick,\n    onPaste: handlePaste,\n    onInput: handleInput,\n    // Other\n    contentEditable: parsedSelectedSections === 'all',\n    tabIndex: parsedSelectedSections === 0 ? -1 : 0 // TODO: Try to set to undefined when there is a section selected.\n  };\n}", "map": {"version": 3, "names": ["useEventCallback", "useTimeout", "useFieldRootHandleKeyDown", "getActiveElement", "syncSelectionToDOM", "useFieldRootProps", "parameters", "manager", "focused", "setFocused", "domGetters", "stateResponse", "applyCharacterEditing", "internalPropsWithDefaults", "parsedSelectedSections", "sectionOrder", "state", "clearValue", "setCharacterQuery", "setSelectedSections", "updateValueFromValueStr", "disabled", "readOnly", "handleKeyDown", "containerClickTimeout", "handleClick", "event", "isReady", "start", "cursorPosition", "document", "getSelection", "getRangeAt", "startOffset", "startIndex", "sectionIndex", "cursorOnStartOfSection", "sections", "length", "section", "startSeparator", "value", "placeholder", "endSeparator", "hasClickedOnASection", "getRoot", "contains", "target", "handleInput", "keyPressed", "textContent", "innerHTML", "map", "join", "charCodeAt", "handlePaste", "preventDefault", "pastedValue", "clipboardData", "getData", "handleFocus", "activeElement", "isFocusInsideASection", "getSectionIndexFromDOMElement", "handleBlur", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "onKeyDown", "onBlur", "onFocus", "onClick", "onPaste", "onInput", "contentEditable", "tabIndex"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldRootProps.js"], "sourcesContent": ["import useEventCallback from '@mui/utils/useEventCallback';\nimport useTimeout from '@mui/utils/useTimeout';\nimport { useFieldRootHandleKeyDown } from \"./useFieldRootHandleKeyDown.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { syncSelectionToDOM } from \"./syncSelectionToDOM.js\";\n\n/**\n * Generate the props to pass to the root element of the field.\n * It is not used by the non-accessible DOM structure (with an <input /> element for editing).\n * It should be used in the MUI accessible DOM structure and the Base UI implementation.\n * @param {UseFieldRootPropsParameters} parameters The parameters of the hook.\n * @returns {UseFieldRootPropsReturnValue} The props to forward to the root element of the field.\n */\nexport function useFieldRootProps(parameters) {\n  const {\n    manager,\n    focused,\n    setFocused,\n    domGetters,\n    stateResponse,\n    applyCharacterEditing,\n    internalPropsWithDefaults,\n    stateResponse: {\n      // States and derived states\n      parsedSelectedSections,\n      sectionOrder,\n      state,\n      // Methods to update the states\n      clearValue,\n      setCharacterQuery,\n      setSelectedSections,\n      updateValueFromValueStr\n    },\n    internalPropsWithDefaults: {\n      disabled = false,\n      readOnly = false\n    }\n  } = parameters;\n\n  // TODO: Inline onContainerKeyDown once the old DOM structure is removed\n  const handleKeyDown = useFieldRootHandleKeyDown({\n    manager,\n    internalPropsWithDefaults,\n    stateResponse\n  });\n  const containerClickTimeout = useTimeout();\n  const handleClick = useEventCallback(event => {\n    if (disabled || !domGetters.isReady()) {\n      return;\n    }\n    setFocused(true);\n    if (parsedSelectedSections === 'all') {\n      containerClickTimeout.start(0, () => {\n        const cursorPosition = document.getSelection().getRangeAt(0).startOffset;\n        if (cursorPosition === 0) {\n          setSelectedSections(sectionOrder.startIndex);\n          return;\n        }\n        let sectionIndex = 0;\n        let cursorOnStartOfSection = 0;\n        while (cursorOnStartOfSection < cursorPosition && sectionIndex < state.sections.length) {\n          const section = state.sections[sectionIndex];\n          sectionIndex += 1;\n          cursorOnStartOfSection += `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`.length;\n        }\n        setSelectedSections(sectionIndex - 1);\n      });\n    } else if (!focused) {\n      setFocused(true);\n      setSelectedSections(sectionOrder.startIndex);\n    } else {\n      const hasClickedOnASection = domGetters.getRoot().contains(event.target);\n      if (!hasClickedOnASection) {\n        setSelectedSections(sectionOrder.startIndex);\n      }\n    }\n  });\n  const handleInput = useEventCallback(event => {\n    if (!domGetters.isReady() || parsedSelectedSections !== 'all') {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    domGetters.getRoot().innerHTML = state.sections.map(section => `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`).join('');\n    syncSelectionToDOM({\n      focused,\n      domGetters,\n      stateResponse\n    });\n    if (keyPressed.length === 0 || keyPressed.charCodeAt(0) === 10) {\n      clearValue();\n      setSelectedSections('all');\n    } else if (keyPressed.length > 1) {\n      updateValueFromValueStr(keyPressed);\n    } else {\n      if (parsedSelectedSections === 'all') {\n        setSelectedSections(0);\n      }\n      applyCharacterEditing({\n        keyPressed,\n        sectionIndex: 0\n      });\n    }\n  });\n  const handlePaste = useEventCallback(event => {\n    if (readOnly || parsedSelectedSections !== 'all') {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    event.preventDefault();\n    setCharacterQuery(null);\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleFocus = useEventCallback(() => {\n    if (focused || disabled || !domGetters.isReady()) {\n      return;\n    }\n    const activeElement = getActiveElement(document);\n    setFocused(true);\n    const isFocusInsideASection = domGetters.getSectionIndexFromDOMElement(activeElement) != null;\n    if (!isFocusInsideASection) {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleBlur = useEventCallback(() => {\n    setTimeout(() => {\n      if (!domGetters.isReady()) {\n        return;\n      }\n      const activeElement = getActiveElement(document);\n      const shouldBlur = !domGetters.getRoot().contains(activeElement);\n      if (shouldBlur) {\n        setFocused(false);\n        setSelectedSections(null);\n      }\n    });\n  });\n  return {\n    // Event handlers\n    onKeyDown: handleKeyDown,\n    onBlur: handleBlur,\n    onFocus: handleFocus,\n    onClick: handleClick,\n    onPaste: handlePaste,\n    onInput: handleInput,\n    // Other\n    contentEditable: parsedSelectedSections === 'all',\n    tabIndex: parsedSelectedSections === 0 ? -1 : 0 // TODO: Try to set to undefined when there is a section selected.\n  };\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,kBAAkB,QAAQ,yBAAyB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EAC5C,MAAM;IACJC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC,aAAa;IACbC,qBAAqB;IACrBC,yBAAyB;IACzBF,aAAa,EAAE;MACb;MACAG,sBAAsB;MACtBC,YAAY;MACZC,KAAK;MACL;MACAC,UAAU;MACVC,iBAAiB;MACjBC,mBAAmB;MACnBC;IACF,CAAC;IACDP,yBAAyB,EAAE;MACzBQ,QAAQ,GAAG,KAAK;MAChBC,QAAQ,GAAG;IACb;EACF,CAAC,GAAGhB,UAAU;;EAEd;EACA,MAAMiB,aAAa,GAAGrB,yBAAyB,CAAC;IAC9CK,OAAO;IACPM,yBAAyB;IACzBF;EACF,CAAC,CAAC;EACF,MAAMa,qBAAqB,GAAGvB,UAAU,CAAC,CAAC;EAC1C,MAAMwB,WAAW,GAAGzB,gBAAgB,CAAC0B,KAAK,IAAI;IAC5C,IAAIL,QAAQ,IAAI,CAACX,UAAU,CAACiB,OAAO,CAAC,CAAC,EAAE;MACrC;IACF;IACAlB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAIK,sBAAsB,KAAK,KAAK,EAAE;MACpCU,qBAAqB,CAACI,KAAK,CAAC,CAAC,EAAE,MAAM;QACnC,MAAMC,cAAc,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,WAAW;QACxE,IAAIJ,cAAc,KAAK,CAAC,EAAE;UACxBV,mBAAmB,CAACJ,YAAY,CAACmB,UAAU,CAAC;UAC5C;QACF;QACA,IAAIC,YAAY,GAAG,CAAC;QACpB,IAAIC,sBAAsB,GAAG,CAAC;QAC9B,OAAOA,sBAAsB,GAAGP,cAAc,IAAIM,YAAY,GAAGnB,KAAK,CAACqB,QAAQ,CAACC,MAAM,EAAE;UACtF,MAAMC,OAAO,GAAGvB,KAAK,CAACqB,QAAQ,CAACF,YAAY,CAAC;UAC5CA,YAAY,IAAI,CAAC;UACjBC,sBAAsB,IAAI,GAAGG,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACI,YAAY,EAAE,CAACL,MAAM;QAC5H;QACAnB,mBAAmB,CAACgB,YAAY,GAAG,CAAC,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC3B,OAAO,EAAE;MACnBC,UAAU,CAAC,IAAI,CAAC;MAChBU,mBAAmB,CAACJ,YAAY,CAACmB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMU,oBAAoB,GAAGlC,UAAU,CAACmC,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACpB,KAAK,CAACqB,MAAM,CAAC;MACxE,IAAI,CAACH,oBAAoB,EAAE;QACzBzB,mBAAmB,CAACJ,YAAY,CAACmB,UAAU,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;EACF,MAAMc,WAAW,GAAGhD,gBAAgB,CAAC0B,KAAK,IAAI;IAC5C,IAAI,CAAChB,UAAU,CAACiB,OAAO,CAAC,CAAC,IAAIb,sBAAsB,KAAK,KAAK,EAAE;MAC7D;IACF;IACA,MAAMiC,MAAM,GAAGrB,KAAK,CAACqB,MAAM;IAC3B,MAAME,UAAU,GAAGF,MAAM,CAACG,WAAW,IAAI,EAAE;IAC3CxC,UAAU,CAACmC,OAAO,CAAC,CAAC,CAACM,SAAS,GAAGnC,KAAK,CAACqB,QAAQ,CAACe,GAAG,CAACb,OAAO,IAAI,GAAGA,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,WAAW,GAAGH,OAAO,CAACI,YAAY,EAAE,CAAC,CAACU,IAAI,CAAC,EAAE,CAAC;IAClKjD,kBAAkB,CAAC;MACjBI,OAAO;MACPE,UAAU;MACVC;IACF,CAAC,CAAC;IACF,IAAIsC,UAAU,CAACX,MAAM,KAAK,CAAC,IAAIW,UAAU,CAACK,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9DrC,UAAU,CAAC,CAAC;MACZE,mBAAmB,CAAC,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAI8B,UAAU,CAACX,MAAM,GAAG,CAAC,EAAE;MAChClB,uBAAuB,CAAC6B,UAAU,CAAC;IACrC,CAAC,MAAM;MACL,IAAInC,sBAAsB,KAAK,KAAK,EAAE;QACpCK,mBAAmB,CAAC,CAAC,CAAC;MACxB;MACAP,qBAAqB,CAAC;QACpBqC,UAAU;QACVd,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMoB,WAAW,GAAGvD,gBAAgB,CAAC0B,KAAK,IAAI;IAC5C,IAAIJ,QAAQ,IAAIR,sBAAsB,KAAK,KAAK,EAAE;MAChDY,KAAK,CAAC8B,cAAc,CAAC,CAAC;MACtB;IACF;IACA,MAAMC,WAAW,GAAG/B,KAAK,CAACgC,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvDjC,KAAK,CAAC8B,cAAc,CAAC,CAAC;IACtBtC,iBAAiB,CAAC,IAAI,CAAC;IACvBE,uBAAuB,CAACqC,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMG,WAAW,GAAG5D,gBAAgB,CAAC,MAAM;IACzC,IAAIQ,OAAO,IAAIa,QAAQ,IAAI,CAACX,UAAU,CAACiB,OAAO,CAAC,CAAC,EAAE;MAChD;IACF;IACA,MAAMkC,aAAa,GAAG1D,gBAAgB,CAAC2B,QAAQ,CAAC;IAChDrB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMqD,qBAAqB,GAAGpD,UAAU,CAACqD,6BAA6B,CAACF,aAAa,CAAC,IAAI,IAAI;IAC7F,IAAI,CAACC,qBAAqB,EAAE;MAC1B3C,mBAAmB,CAACJ,YAAY,CAACmB,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAM8B,UAAU,GAAGhE,gBAAgB,CAAC,MAAM;IACxCiE,UAAU,CAAC,MAAM;MACf,IAAI,CAACvD,UAAU,CAACiB,OAAO,CAAC,CAAC,EAAE;QACzB;MACF;MACA,MAAMkC,aAAa,GAAG1D,gBAAgB,CAAC2B,QAAQ,CAAC;MAChD,MAAMoC,UAAU,GAAG,CAACxD,UAAU,CAACmC,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACe,aAAa,CAAC;MAChE,IAAIK,UAAU,EAAE;QACdzD,UAAU,CAAC,KAAK,CAAC;QACjBU,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO;IACL;IACAgD,SAAS,EAAE5C,aAAa;IACxB6C,MAAM,EAAEJ,UAAU;IAClBK,OAAO,EAAET,WAAW;IACpBU,OAAO,EAAE7C,WAAW;IACpB8C,OAAO,EAAEhB,WAAW;IACpBiB,OAAO,EAAExB,WAAW;IACpB;IACAyB,eAAe,EAAE3D,sBAAsB,KAAK,KAAK;IACjD4D,QAAQ,EAAE5D,sBAAsB,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAClD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}