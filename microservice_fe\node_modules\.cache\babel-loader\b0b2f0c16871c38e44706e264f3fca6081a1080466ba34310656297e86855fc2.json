{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPopperUtilityClass(slot) {\n  return generateUtilityClass('MuiPopper', slot);\n}\nconst popperClasses = generateUtilityClasses('MuiPopper', ['root']);\nexport default popperClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getPopperUtilityClass", "slot", "popperClasses"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/esm/Popper/popperClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPopperUtilityClass(slot) {\n  return generateUtilityClass('MuiPopper', slot);\n}\nconst popperClasses = generateUtilityClasses('MuiPopper', ['root']);\nexport default popperClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;AACnE,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}