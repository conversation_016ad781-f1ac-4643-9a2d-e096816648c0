{"ast": null, "code": "\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CssVarsProvider = void 0;\nexports.Experimental_CssVarsProvider = Experimental_CssVarsProvider;\nexports.useColorScheme = exports.getInitColorSchemeScript = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styleFunctionSx = _interopRequireDefault(require(\"@mui/system/styleFunctionSx\"));\nvar _system = require(\"@mui/system\");\nvar _createTheme = _interopRequireDefault(require(\"./createTheme\"));\nvar _createTypography = _interopRequireDefault(require(\"./createTypography\"));\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nvar _InitColorSchemeScript = require(\"../InitColorSchemeScript/InitColorSchemeScript\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst {\n  CssVarsProvider: InternalCssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: deprecatedGetInitColorSchemeScript\n} = (0, _system.unstable_createCssVarsProvider)({\n  themeId: _identifier.default,\n  // @ts-ignore ignore module augmentation tests\n  theme: () => (0, _createTheme.default)({\n    cssVariables: true\n  }),\n  colorSchemeStorageKey: _InitColorSchemeScript.defaultConfig.colorSchemeStorageKey,\n  modeStorageKey: _InitColorSchemeScript.defaultConfig.modeStorageKey,\n  defaultColorScheme: {\n    light: _InitColorSchemeScript.defaultConfig.defaultLightColorScheme,\n    dark: _InitColorSchemeScript.defaultConfig.defaultDarkColorScheme\n  },\n  resolveTheme: theme => {\n    const newTheme = {\n      ...theme,\n      typography: (0, _createTypography.default)(theme.palette, theme.typography)\n    };\n    newTheme.unstable_sx = function sx(props) {\n      return (0, _styleFunctionSx.default)({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  }\n});\nexports.useColorScheme = useColorScheme;\nlet warnedOnce = false;\n\n// TODO: remove in v7\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction Experimental_CssVarsProvider(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      console.warn(['MUI: The Experimental_CssVarsProvider component has been ported into ThemeProvider.', '', \"You should use `import { ThemeProvider } from '@mui/material/styles'` instead.\", 'For more details, check out https://mui.com/material-ui/customization/css-theme-variables/usage/'].join('\\n'));\n      warnedOnce = true;\n    }\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(InternalCssVarsProvider, {\n    ...props\n  });\n}\nlet warnedInitScriptOnce = false;\n\n// TODO: remove in v7\nconst getInitColorSchemeScript = params => {\n  if (!warnedInitScriptOnce) {\n    console.warn(['MUI: The getInitColorSchemeScript function has been deprecated.', '', \"You should use `import InitColorSchemeScript from '@mui/material/InitColorSchemeScript'`\", 'and replace the function call with `<InitColorSchemeScript />` instead.'].join('\\n'));\n    warnedInitScriptOnce = true;\n  }\n  return deprecatedGetInitColorSchemeScript(params);\n};\n\n/**\n * TODO: remove this export in v7\n * @deprecated\n * The `CssVarsProvider` component has been deprecated and ported into `ThemeProvider`.\n *\n * You should use `ThemeProvider` and `createTheme()` instead:\n *\n * ```diff\n * - import { CssVarsProvider, extendTheme } from '@mui/material/styles';\n * + import { ThemeProvider, createTheme } from '@mui/material/styles';\n *\n * - const theme = extendTheme();\n * + const theme = createTheme({\n * +   cssVariables: true,\n * +   colorSchemes: { light: true, dark: true },\n * + });\n *\n * - <CssVarsProvider theme={theme}>\n * + <ThemeProvider theme={theme}>\n * ```\n *\n * To see the full documentation, check out https://mui.com/material-ui/customization/css-theme-variables/usage/.\n */\nexports.getInitColorSchemeScript = getInitColorSchemeScript;\nconst CssVarsProvider = exports.CssVarsProvider = InternalCssVarsProvider;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "CssVarsProvider", "Experimental_CssVarsProvider", "useColorScheme", "getInitColorSchemeScript", "React", "_styleFunctionSx", "_system", "_createTheme", "_createTypography", "_identifier", "_InitColorSchemeScript", "_jsxRuntime", "InternalCssVarsProvider", "deprecatedGetInitColorSchemeScript", "unstable_createCssVarsProvider", "themeId", "theme", "cssVariables", "colorSchemeStorageKey", "defaultConfig", "modeStorageKey", "defaultColorScheme", "light", "defaultLightColorScheme", "dark", "defaultDarkColorScheme", "resolveTheme", "newTheme", "typography", "palette", "unstable_sx", "sx", "props", "warnedOnce", "process", "env", "NODE_ENV", "console", "warn", "join", "jsx", "warnedInitScriptOnce", "params"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/ThemeProviderWithVars.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CssVarsProvider = void 0;\nexports.Experimental_CssVarsProvider = Experimental_CssVarsProvider;\nexports.useColorScheme = exports.getInitColorSchemeScript = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styleFunctionSx = _interopRequireDefault(require(\"@mui/system/styleFunctionSx\"));\nvar _system = require(\"@mui/system\");\nvar _createTheme = _interopRequireDefault(require(\"./createTheme\"));\nvar _createTypography = _interopRequireDefault(require(\"./createTypography\"));\nvar _identifier = _interopRequireDefault(require(\"./identifier\"));\nvar _InitColorSchemeScript = require(\"../InitColorSchemeScript/InitColorSchemeScript\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst {\n  CssVarsProvider: InternalCssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: deprecatedGetInitColorSchemeScript\n} = (0, _system.unstable_createCssVarsProvider)({\n  themeId: _identifier.default,\n  // @ts-ignore ignore module augmentation tests\n  theme: () => (0, _createTheme.default)({\n    cssVariables: true\n  }),\n  colorSchemeStorageKey: _InitColorSchemeScript.defaultConfig.colorSchemeStorageKey,\n  modeStorageKey: _InitColorSchemeScript.defaultConfig.modeStorageKey,\n  defaultColorScheme: {\n    light: _InitColorSchemeScript.defaultConfig.defaultLightColorScheme,\n    dark: _InitColorSchemeScript.defaultConfig.defaultDarkColorScheme\n  },\n  resolveTheme: theme => {\n    const newTheme = {\n      ...theme,\n      typography: (0, _createTypography.default)(theme.palette, theme.typography)\n    };\n    newTheme.unstable_sx = function sx(props) {\n      return (0, _styleFunctionSx.default)({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  }\n});\nexports.useColorScheme = useColorScheme;\nlet warnedOnce = false;\n\n// TODO: remove in v7\n// eslint-disable-next-line @typescript-eslint/naming-convention\nfunction Experimental_CssVarsProvider(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      console.warn(['MUI: The Experimental_CssVarsProvider component has been ported into ThemeProvider.', '', \"You should use `import { ThemeProvider } from '@mui/material/styles'` instead.\", 'For more details, check out https://mui.com/material-ui/customization/css-theme-variables/usage/'].join('\\n'));\n      warnedOnce = true;\n    }\n  }\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(InternalCssVarsProvider, {\n    ...props\n  });\n}\nlet warnedInitScriptOnce = false;\n\n// TODO: remove in v7\nconst getInitColorSchemeScript = params => {\n  if (!warnedInitScriptOnce) {\n    console.warn(['MUI: The getInitColorSchemeScript function has been deprecated.', '', \"You should use `import InitColorSchemeScript from '@mui/material/InitColorSchemeScript'`\", 'and replace the function call with `<InitColorSchemeScript />` instead.'].join('\\n'));\n    warnedInitScriptOnce = true;\n  }\n  return deprecatedGetInitColorSchemeScript(params);\n};\n\n/**\n * TODO: remove this export in v7\n * @deprecated\n * The `CssVarsProvider` component has been deprecated and ported into `ThemeProvider`.\n *\n * You should use `ThemeProvider` and `createTheme()` instead:\n *\n * ```diff\n * - import { CssVarsProvider, extendTheme } from '@mui/material/styles';\n * + import { ThemeProvider, createTheme } from '@mui/material/styles';\n *\n * - const theme = extendTheme();\n * + const theme = createTheme({\n * +   cssVariables: true,\n * +   colorSchemes: { light: true, dark: true },\n * + });\n *\n * - <CssVarsProvider theme={theme}>\n * + <ThemeProvider theme={theme}>\n * ```\n *\n * To see the full documentation, check out https://mui.com/material-ui/customization/css-theme-variables/usage/.\n */\nexports.getInitColorSchemeScript = getInitColorSchemeScript;\nconst CssVarsProvider = exports.CssVarsProvider = InternalCssVarsProvider;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChCF,OAAO,CAACG,4BAA4B,GAAGA,4BAA4B;AACnEH,OAAO,CAACI,cAAc,GAAGJ,OAAO,CAACK,wBAAwB,GAAG,KAAK,CAAC;AAClE,IAAIC,KAAK,GAAGT,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIY,gBAAgB,GAAGb,sBAAsB,CAACC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACrF,IAAIa,OAAO,GAAGb,OAAO,CAAC,aAAa,CAAC;AACpC,IAAIc,YAAY,GAAGf,sBAAsB,CAACC,OAAO,CAAC,eAAe,CAAC,CAAC;AACnE,IAAIe,iBAAiB,GAAGhB,sBAAsB,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC7E,IAAIgB,WAAW,GAAGjB,sBAAsB,CAACC,OAAO,CAAC,cAAc,CAAC,CAAC;AACjE,IAAIiB,sBAAsB,GAAGjB,OAAO,CAAC,gDAAgD,CAAC;AACtF,IAAIkB,WAAW,GAAGlB,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAM;EACJO,eAAe,EAAEY,uBAAuB;EACxCV,cAAc;EACdC,wBAAwB,EAAEU;AAC5B,CAAC,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACQ,8BAA8B,EAAE;EAC9CC,OAAO,EAAEN,WAAW,CAACf,OAAO;EAC5B;EACAsB,KAAK,EAAEA,CAAA,KAAM,CAAC,CAAC,EAAET,YAAY,CAACb,OAAO,EAAE;IACrCuB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,qBAAqB,EAAER,sBAAsB,CAACS,aAAa,CAACD,qBAAqB;EACjFE,cAAc,EAAEV,sBAAsB,CAACS,aAAa,CAACC,cAAc;EACnEC,kBAAkB,EAAE;IAClBC,KAAK,EAAEZ,sBAAsB,CAACS,aAAa,CAACI,uBAAuB;IACnEC,IAAI,EAAEd,sBAAsB,CAACS,aAAa,CAACM;EAC7C,CAAC;EACDC,YAAY,EAAEV,KAAK,IAAI;IACrB,MAAMW,QAAQ,GAAG;MACf,GAAGX,KAAK;MACRY,UAAU,EAAE,CAAC,CAAC,EAAEpB,iBAAiB,CAACd,OAAO,EAAEsB,KAAK,CAACa,OAAO,EAAEb,KAAK,CAACY,UAAU;IAC5E,CAAC;IACDD,QAAQ,CAACG,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;MACxC,OAAO,CAAC,CAAC,EAAE3B,gBAAgB,CAACX,OAAO,EAAE;QACnCqC,EAAE,EAAEC,KAAK;QACThB,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC;IACD,OAAOW,QAAQ;EACjB;AACF,CAAC,CAAC;AACF7B,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvC,IAAI+B,UAAU,GAAG,KAAK;;AAEtB;AACA;AACA,SAAShC,4BAA4BA,CAAC+B,KAAK,EAAE;EAC3C,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACH,UAAU,EAAE;MACfI,OAAO,CAACC,IAAI,CAAC,CAAC,qFAAqF,EAAE,EAAE,EAAE,gFAAgF,EAAE,kGAAkG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1SN,UAAU,GAAG,IAAI;IACnB;EACF;EACA,OAAO,aAAa,CAAC,CAAC,EAAEtB,WAAW,CAAC6B,GAAG,EAAE5B,uBAAuB,EAAE;IAChE,GAAGoB;EACL,CAAC,CAAC;AACJ;AACA,IAAIS,oBAAoB,GAAG,KAAK;;AAEhC;AACA,MAAMtC,wBAAwB,GAAGuC,MAAM,IAAI;EACzC,IAAI,CAACD,oBAAoB,EAAE;IACzBJ,OAAO,CAACC,IAAI,CAAC,CAAC,iEAAiE,EAAE,EAAE,EAAE,0FAA0F,EAAE,yEAAyE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvQE,oBAAoB,GAAG,IAAI;EAC7B;EACA,OAAO5B,kCAAkC,CAAC6B,MAAM,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA5C,OAAO,CAACK,wBAAwB,GAAGA,wBAAwB;AAC3D,MAAMH,eAAe,GAAGF,OAAO,CAACE,eAAe,GAAGY,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}