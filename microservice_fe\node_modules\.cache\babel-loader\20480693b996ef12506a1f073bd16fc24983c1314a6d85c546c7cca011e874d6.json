{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PickerContext } from \"../../hooks/usePickerContext.js\";\n\n/**\n * Returns the context passed by the Picker wrapping the current component.\n * If the context is not found, returns `null`.\n */\nexport const useNullablePickerContext = () => React.useContext(PickerContext);", "map": {"version": 3, "names": ["React", "<PERSON>er<PERSON>ontext", "useNullablePickerContext", "useContext"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useNullablePickerContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { PickerContext } from \"../../hooks/usePickerContext.js\";\n\n/**\n * Returns the context passed by the Picker wrapping the current component.\n * If the context is not found, returns `null`.\n */\nexport const useNullablePickerContext = () => React.useContext(PickerContext);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,iCAAiC;;AAE/D;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGA,CAAA,KAAMF,KAAK,CAACG,UAAU,CAACF,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}