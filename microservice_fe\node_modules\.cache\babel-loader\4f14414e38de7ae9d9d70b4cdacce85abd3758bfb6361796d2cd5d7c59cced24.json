{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useNullablePickerContext } from \"../useNullablePickerContext.js\";\nimport { useNullableFieldPrivateContext } from \"../useNullableFieldPrivateContext.js\";\n\n/**\n * Applies the default values to the field internal props.\n * This is a temporary hook that will be removed during a follow up when `useField` will receive the internal props without the defaults.\n * It is only here to allow the migration to be done in smaller steps.\n */\nexport function useFieldInternalPropsWithDefaults(parameters) {\n  const {\n    manager: {\n      internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToFieldInternalProps\n    },\n    internalProps,\n    skipContextFieldRefAssignment\n  } = parameters;\n  const pickerContext = useNullablePickerContext();\n  const fieldPrivateContext = useNullableFieldPrivateContext();\n  const handleFieldRef = useForkRef(internalProps.unstableFieldRef, skipContextFieldRefAssignment ? null : fieldPrivateContext?.fieldRef);\n  const setValue = pickerContext?.setValue;\n  const handleChangeFromPicker = React.useCallback((newValue, ctx) => {\n    return setValue?.(newValue, {\n      validationError: ctx.validationError,\n      shouldClose: false\n    });\n  }, [setValue]);\n  const internalPropsWithDefaultsFromContext = React.useMemo(() => {\n    // If one of the context is null,\n    // Then the field is used as a standalone component and the other context will be null as well.\n    if (fieldPrivateContext != null && pickerContext != null) {\n      return _extends({\n        value: pickerContext.value,\n        onChange: handleChangeFromPicker,\n        timezone: pickerContext.timezone,\n        disabled: pickerContext.disabled,\n        readOnly: pickerContext.readOnly,\n        autoFocus: pickerContext.autoFocus && !pickerContext.open,\n        focused: pickerContext.open ? true : undefined,\n        format: pickerContext.fieldFormat,\n        formatDensity: fieldPrivateContext.formatDensity,\n        enableAccessibleFieldDOMStructure: fieldPrivateContext.enableAccessibleFieldDOMStructure,\n        selectedSections: fieldPrivateContext.selectedSections,\n        onSelectedSectionsChange: fieldPrivateContext.onSelectedSectionsChange,\n        unstableFieldRef: handleFieldRef\n      }, internalProps);\n    }\n    return internalProps;\n  }, [pickerContext, fieldPrivateContext, internalProps, handleChangeFromPicker, handleFieldRef]);\n  return useApplyDefaultValuesToFieldInternalProps(internalPropsWithDefaultsFromContext);\n}", "map": {"version": 3, "names": ["_extends", "React", "useForkRef", "useNullablePickerContext", "useNullableFieldPrivateContext", "useFieldInternalPropsWithDefaults", "parameters", "manager", "internal_useApplyDefaultValuesToFieldInternalProps", "useApplyDefaultValuesToFieldInternalProps", "internalProps", "skipContextFieldRefAssignment", "picker<PERSON>ontext", "fieldPrivateContext", "handleFieldRef", "unstableFieldRef", "fieldRef", "setValue", "handleChangeFromPicker", "useCallback", "newValue", "ctx", "validationError", "shouldClose", "internalPropsWithDefaultsFromContext", "useMemo", "value", "onChange", "timezone", "disabled", "readOnly", "autoFocus", "open", "focused", "undefined", "format", "fieldFormat", "formatDensity", "enableAccessibleFieldDOMStructure", "selectedSections", "onSelectedSectionsChange"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldInternalPropsWithDefaults.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useNullablePickerContext } from \"../useNullablePickerContext.js\";\nimport { useNullableFieldPrivateContext } from \"../useNullableFieldPrivateContext.js\";\n\n/**\n * Applies the default values to the field internal props.\n * This is a temporary hook that will be removed during a follow up when `useField` will receive the internal props without the defaults.\n * It is only here to allow the migration to be done in smaller steps.\n */\nexport function useFieldInternalPropsWithDefaults(parameters) {\n  const {\n    manager: {\n      internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToFieldInternalProps\n    },\n    internalProps,\n    skipContextFieldRefAssignment\n  } = parameters;\n  const pickerContext = useNullablePickerContext();\n  const fieldPrivateContext = useNullableFieldPrivateContext();\n  const handleFieldRef = useForkRef(internalProps.unstableFieldRef, skipContextFieldRefAssignment ? null : fieldPrivateContext?.fieldRef);\n  const setValue = pickerContext?.setValue;\n  const handleChangeFromPicker = React.useCallback((newValue, ctx) => {\n    return setValue?.(newValue, {\n      validationError: ctx.validationError,\n      shouldClose: false\n    });\n  }, [setValue]);\n  const internalPropsWithDefaultsFromContext = React.useMemo(() => {\n    // If one of the context is null,\n    // Then the field is used as a standalone component and the other context will be null as well.\n    if (fieldPrivateContext != null && pickerContext != null) {\n      return _extends({\n        value: pickerContext.value,\n        onChange: handleChangeFromPicker,\n        timezone: pickerContext.timezone,\n        disabled: pickerContext.disabled,\n        readOnly: pickerContext.readOnly,\n        autoFocus: pickerContext.autoFocus && !pickerContext.open,\n        focused: pickerContext.open ? true : undefined,\n        format: pickerContext.fieldFormat,\n        formatDensity: fieldPrivateContext.formatDensity,\n        enableAccessibleFieldDOMStructure: fieldPrivateContext.enableAccessibleFieldDOMStructure,\n        selectedSections: fieldPrivateContext.selectedSections,\n        onSelectedSectionsChange: fieldPrivateContext.onSelectedSectionsChange,\n        unstableFieldRef: handleFieldRef\n      }, internalProps);\n    }\n    return internalProps;\n  }, [pickerContext, fieldPrivateContext, internalProps, handleChangeFromPicker, handleFieldRef]);\n  return useApplyDefaultValuesToFieldInternalProps(internalPropsWithDefaultsFromContext);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,wBAAwB,QAAQ,gCAAgC;AACzE,SAASC,8BAA8B,QAAQ,sCAAsC;;AAErF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iCAAiCA,CAACC,UAAU,EAAE;EAC5D,MAAM;IACJC,OAAO,EAAE;MACPC,kDAAkD,EAAEC;IACtD,CAAC;IACDC,aAAa;IACbC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,aAAa,GAAGT,wBAAwB,CAAC,CAAC;EAChD,MAAMU,mBAAmB,GAAGT,8BAA8B,CAAC,CAAC;EAC5D,MAAMU,cAAc,GAAGZ,UAAU,CAACQ,aAAa,CAACK,gBAAgB,EAAEJ,6BAA6B,GAAG,IAAI,GAAGE,mBAAmB,EAAEG,QAAQ,CAAC;EACvI,MAAMC,QAAQ,GAAGL,aAAa,EAAEK,QAAQ;EACxC,MAAMC,sBAAsB,GAAGjB,KAAK,CAACkB,WAAW,CAAC,CAACC,QAAQ,EAAEC,GAAG,KAAK;IAClE,OAAOJ,QAAQ,GAAGG,QAAQ,EAAE;MAC1BE,eAAe,EAAED,GAAG,CAACC,eAAe;MACpCC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACd,MAAMO,oCAAoC,GAAGvB,KAAK,CAACwB,OAAO,CAAC,MAAM;IAC/D;IACA;IACA,IAAIZ,mBAAmB,IAAI,IAAI,IAAID,aAAa,IAAI,IAAI,EAAE;MACxD,OAAOZ,QAAQ,CAAC;QACd0B,KAAK,EAAEd,aAAa,CAACc,KAAK;QAC1BC,QAAQ,EAAET,sBAAsB;QAChCU,QAAQ,EAAEhB,aAAa,CAACgB,QAAQ;QAChCC,QAAQ,EAAEjB,aAAa,CAACiB,QAAQ;QAChCC,QAAQ,EAAElB,aAAa,CAACkB,QAAQ;QAChCC,SAAS,EAAEnB,aAAa,CAACmB,SAAS,IAAI,CAACnB,aAAa,CAACoB,IAAI;QACzDC,OAAO,EAAErB,aAAa,CAACoB,IAAI,GAAG,IAAI,GAAGE,SAAS;QAC9CC,MAAM,EAAEvB,aAAa,CAACwB,WAAW;QACjCC,aAAa,EAAExB,mBAAmB,CAACwB,aAAa;QAChDC,iCAAiC,EAAEzB,mBAAmB,CAACyB,iCAAiC;QACxFC,gBAAgB,EAAE1B,mBAAmB,CAAC0B,gBAAgB;QACtDC,wBAAwB,EAAE3B,mBAAmB,CAAC2B,wBAAwB;QACtEzB,gBAAgB,EAAED;MACpB,CAAC,EAAEJ,aAAa,CAAC;IACnB;IACA,OAAOA,aAAa;EACtB,CAAC,EAAE,CAACE,aAAa,EAAEC,mBAAmB,EAAEH,aAAa,EAAEQ,sBAAsB,EAAEJ,cAAc,CAAC,CAAC;EAC/F,OAAOL,yCAAyC,CAACe,oCAAoC,CAAC;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}