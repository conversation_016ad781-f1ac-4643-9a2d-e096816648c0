{"ast": null, "code": "export const getHourSectionOptions = ({\n  now,\n  value,\n  utils,\n  ampm,\n  isDisabled,\n  resolveAriaLabel,\n  timeStep,\n  valueOrReferenceDate\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const result = [];\n  const isSelected = (hour, overriddenCurrentHours) => {\n    const resolvedCurrentHours = overriddenCurrentHours ?? currentHours;\n    if (resolvedCurrentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return resolvedCurrentHours === 12 || resolvedCurrentHours === 0;\n      }\n      return resolvedCurrentHours === hour || resolvedCurrentHours - 12 === hour;\n    }\n    return resolvedCurrentHours === hour;\n  };\n  const isFocused = hour => {\n    return isSelected(hour, utils.getHours(valueOrReferenceDate));\n  };\n  const endHour = ampm ? 11 : 23;\n  for (let hour = 0; hour <= endHour; hour += timeStep) {\n    let label = utils.format(utils.setHours(now, hour), ampm ? 'hours12h' : 'hours24h');\n    const ariaLabel = resolveAriaLabel(parseInt(label, 10).toString());\n    label = utils.formatNumber(label);\n    result.push({\n      value: hour,\n      label,\n      isSelected,\n      isDisabled,\n      isFocused,\n      ariaLabel\n    });\n  }\n  return result;\n};\nexport const getTimeSectionOptions = ({\n  value,\n  utils,\n  isDisabled,\n  timeStep,\n  resolveLabel,\n  resolveAriaLabel,\n  hasValue = true\n}) => {\n  const isSelected = timeValue => {\n    if (value === null) {\n      return false;\n    }\n    return hasValue && value === timeValue;\n  };\n  const isFocused = timeValue => {\n    return value === timeValue;\n  };\n  return [...Array.from({\n    length: Math.ceil(60 / timeStep)\n  }, (_, index) => {\n    const timeValue = timeStep * index;\n    return {\n      value: timeValue,\n      label: utils.formatNumber(resolveLabel(timeValue)),\n      isDisabled,\n      isSelected,\n      isFocused,\n      ariaLabel: resolveAriaLabel(timeValue.toString())\n    };\n  })];\n};", "map": {"version": 3, "names": ["getHourSectionOptions", "now", "value", "utils", "ampm", "isDisabled", "resolveAriaLabel", "timeStep", "valueOrReferenceDate", "currentHours", "getHours", "result", "isSelected", "hour", "overriddenCurrentHours", "resolvedCurrentHours", "isFocused", "endHour", "label", "format", "setHours", "aria<PERSON><PERSON><PERSON>", "parseInt", "toString", "formatNumber", "push", "getTimeSectionOptions", "resolve<PERSON>abel", "hasValue", "timeValue", "Array", "from", "length", "Math", "ceil", "_", "index"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/MultiSectionDigitalClock/MultiSectionDigitalClock.utils.js"], "sourcesContent": ["export const getHourSectionOptions = ({\n  now,\n  value,\n  utils,\n  ampm,\n  isDisabled,\n  resolveAriaLabel,\n  timeStep,\n  valueOrReferenceDate\n}) => {\n  const currentHours = value ? utils.getHours(value) : null;\n  const result = [];\n  const isSelected = (hour, overriddenCurrentHours) => {\n    const resolvedCurrentHours = overriddenCurrentHours ?? currentHours;\n    if (resolvedCurrentHours === null) {\n      return false;\n    }\n    if (ampm) {\n      if (hour === 12) {\n        return resolvedCurrentHours === 12 || resolvedCurrentHours === 0;\n      }\n      return resolvedCurrentHours === hour || resolvedCurrentHours - 12 === hour;\n    }\n    return resolvedCurrentHours === hour;\n  };\n  const isFocused = hour => {\n    return isSelected(hour, utils.getHours(valueOrReferenceDate));\n  };\n  const endHour = ampm ? 11 : 23;\n  for (let hour = 0; hour <= endHour; hour += timeStep) {\n    let label = utils.format(utils.setHours(now, hour), ampm ? 'hours12h' : 'hours24h');\n    const ariaLabel = resolveAriaLabel(parseInt(label, 10).toString());\n    label = utils.formatNumber(label);\n    result.push({\n      value: hour,\n      label,\n      isSelected,\n      isDisabled,\n      isFocused,\n      ariaLabel\n    });\n  }\n  return result;\n};\nexport const getTimeSectionOptions = ({\n  value,\n  utils,\n  isDisabled,\n  timeStep,\n  resolveLabel,\n  resolveAriaLabel,\n  hasValue = true\n}) => {\n  const isSelected = timeValue => {\n    if (value === null) {\n      return false;\n    }\n    return hasValue && value === timeValue;\n  };\n  const isFocused = timeValue => {\n    return value === timeValue;\n  };\n  return [...Array.from({\n    length: Math.ceil(60 / timeStep)\n  }, (_, index) => {\n    const timeValue = timeStep * index;\n    return {\n      value: timeValue,\n      label: utils.formatNumber(resolveLabel(timeValue)),\n      isDisabled,\n      isSelected,\n      isFocused,\n      ariaLabel: resolveAriaLabel(timeValue.toString())\n    };\n  })];\n};"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAGA,CAAC;EACpCC,GAAG;EACHC,KAAK;EACLC,KAAK;EACLC,IAAI;EACJC,UAAU;EACVC,gBAAgB;EAChBC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAGP,KAAK,GAAGC,KAAK,CAACO,QAAQ,CAACR,KAAK,CAAC,GAAG,IAAI;EACzD,MAAMS,MAAM,GAAG,EAAE;EACjB,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,sBAAsB,KAAK;IACnD,MAAMC,oBAAoB,GAAGD,sBAAsB,IAAIL,YAAY;IACnE,IAAIM,oBAAoB,KAAK,IAAI,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAIX,IAAI,EAAE;MACR,IAAIS,IAAI,KAAK,EAAE,EAAE;QACf,OAAOE,oBAAoB,KAAK,EAAE,IAAIA,oBAAoB,KAAK,CAAC;MAClE;MACA,OAAOA,oBAAoB,KAAKF,IAAI,IAAIE,oBAAoB,GAAG,EAAE,KAAKF,IAAI;IAC5E;IACA,OAAOE,oBAAoB,KAAKF,IAAI;EACtC,CAAC;EACD,MAAMG,SAAS,GAAGH,IAAI,IAAI;IACxB,OAAOD,UAAU,CAACC,IAAI,EAAEV,KAAK,CAACO,QAAQ,CAACF,oBAAoB,CAAC,CAAC;EAC/D,CAAC;EACD,MAAMS,OAAO,GAAGb,IAAI,GAAG,EAAE,GAAG,EAAE;EAC9B,KAAK,IAAIS,IAAI,GAAG,CAAC,EAAEA,IAAI,IAAII,OAAO,EAAEJ,IAAI,IAAIN,QAAQ,EAAE;IACpD,IAAIW,KAAK,GAAGf,KAAK,CAACgB,MAAM,CAAChB,KAAK,CAACiB,QAAQ,CAACnB,GAAG,EAAEY,IAAI,CAAC,EAAET,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC;IACnF,MAAMiB,SAAS,GAAGf,gBAAgB,CAACgB,QAAQ,CAACJ,KAAK,EAAE,EAAE,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;IAClEL,KAAK,GAAGf,KAAK,CAACqB,YAAY,CAACN,KAAK,CAAC;IACjCP,MAAM,CAACc,IAAI,CAAC;MACVvB,KAAK,EAAEW,IAAI;MACXK,KAAK;MACLN,UAAU;MACVP,UAAU;MACVW,SAAS;MACTK;IACF,CAAC,CAAC;EACJ;EACA,OAAOV,MAAM;AACf,CAAC;AACD,OAAO,MAAMe,qBAAqB,GAAGA,CAAC;EACpCxB,KAAK;EACLC,KAAK;EACLE,UAAU;EACVE,QAAQ;EACRoB,YAAY;EACZrB,gBAAgB;EAChBsB,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,MAAMhB,UAAU,GAAGiB,SAAS,IAAI;IAC9B,IAAI3B,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAO0B,QAAQ,IAAI1B,KAAK,KAAK2B,SAAS;EACxC,CAAC;EACD,MAAMb,SAAS,GAAGa,SAAS,IAAI;IAC7B,OAAO3B,KAAK,KAAK2B,SAAS;EAC5B,CAAC;EACD,OAAO,CAAC,GAAGC,KAAK,CAACC,IAAI,CAAC;IACpBC,MAAM,EAAEC,IAAI,CAACC,IAAI,CAAC,EAAE,GAAG3B,QAAQ;EACjC,CAAC,EAAE,CAAC4B,CAAC,EAAEC,KAAK,KAAK;IACf,MAAMP,SAAS,GAAGtB,QAAQ,GAAG6B,KAAK;IAClC,OAAO;MACLlC,KAAK,EAAE2B,SAAS;MAChBX,KAAK,EAAEf,KAAK,CAACqB,YAAY,CAACG,YAAY,CAACE,SAAS,CAAC,CAAC;MAClDxB,UAAU;MACVO,UAAU;MACVI,SAAS;MACTK,SAAS,EAAEf,gBAAgB,CAACuB,SAAS,CAACN,QAAQ,CAAC,CAAC;IAClD,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}