{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.defaultConfig = exports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _InitColorSchemeScript = _interopRequireDefault(require(\"@mui/system/InitColorSchemeScript\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst defaultConfig = exports.defaultConfig = {\n  attribute: 'data-mui-color-scheme',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultLightColorScheme: 'light',\n  defaultDarkColorScheme: 'dark',\n  modeStorageKey: 'mui-mode'\n};\n/**\n *\n * Demos:\n *\n * - [InitColorSchemeScript](https://mui.com/material-ui/react-init-color-scheme-script/)\n *\n * API:\n *\n * - [InitColorSchemeScript API](https://mui.com/material-ui/api/init-color-scheme-script/)\n */\nfunction InitColorSchemeScript(props) {\n  const {\n    defaultMode = 'system',\n    defaultLightColorScheme = defaultConfig.defaultLightColorScheme,\n    defaultDarkColorScheme = defaultConfig.defaultDarkColorScheme,\n    modeStorageKey = defaultConfig.modeStorageKey,\n    colorSchemeStorageKey = defaultConfig.colorSchemeStorageKey,\n    attribute: initialAttribute = defaultConfig.attribute,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = props;\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_InitColorSchemeScript.default, {\n    defaultMode: defaultMode,\n    defaultLightColorScheme: defaultLightColorScheme,\n    defaultDarkColorScheme: defaultDarkColorScheme,\n    modeStorageKey: modeStorageKey,\n    colorSchemeStorageKey: colorSchemeStorageKey,\n    attribute: initialAttribute,\n    colorSchemeNode: colorSchemeNode,\n    nonce: nonce\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? InitColorSchemeScript.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * DOM attribute for applying a color scheme.\n   * @default 'data-mui-color-scheme'\n   * @example '.mode-%s' // for class based color scheme\n   * @example '[data-mode-%s]' // for data-attribute without '='\n   */\n  attribute: _propTypes.default.string,\n  /**\n   * The node (provided as string) used to attach the color-scheme attribute.\n   * @default 'document.documentElement'\n   */\n  colorSchemeNode: _propTypes.default.string,\n  /**\n   * localStorage key used to store `colorScheme`.\n   * @default 'mui-color-scheme'\n   */\n  colorSchemeStorageKey: _propTypes.default.string,\n  /**\n   * The default color scheme to be used in dark mode.\n   * @default 'dark'\n   */\n  defaultDarkColorScheme: _propTypes.default.string,\n  /**\n   * The default color scheme to be used in light mode.\n   * @default 'light'\n   */\n  defaultLightColorScheme: _propTypes.default.string,\n  /**\n   * The default mode when the storage is empty (user's first visit).\n   * @default 'system'\n   */\n  defaultMode: _propTypes.default.oneOf(['dark', 'light', 'system']),\n  /**\n   * localStorage key used to store `mode`.\n   * @default 'mui-mode'\n   */\n  modeStorageKey: _propTypes.default.string,\n  /**\n   * Nonce string to pass to the inline script for CSP headers.\n   */\n  nonce: _propTypes.default.string\n} : void 0;\nvar _default = exports.default = InitColorSchemeScript;", "map": {"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "Object", "defineProperty", "exports", "value", "defaultConfig", "React", "_propTypes", "_InitColorSchemeScript", "_jsxRuntime", "attribute", "colorSchemeStorageKey", "defaultLightColorScheme", "defaultDarkColorScheme", "modeStorageKey", "InitColorSchemeScript", "props", "defaultMode", "initialAttribute", "colorSchemeNode", "nonce", "jsx", "process", "env", "NODE_ENV", "propTypes", "string", "oneOf", "_default"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/InitColorSchemeScript/InitColorSchemeScript.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.defaultConfig = exports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _InitColorSchemeScript = _interopRequireDefault(require(\"@mui/system/InitColorSchemeScript\"));\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nconst defaultConfig = exports.defaultConfig = {\n  attribute: 'data-mui-color-scheme',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultLightColorScheme: 'light',\n  defaultDarkColorScheme: 'dark',\n  modeStorageKey: 'mui-mode'\n};\n/**\n *\n * Demos:\n *\n * - [InitColorSchemeScript](https://mui.com/material-ui/react-init-color-scheme-script/)\n *\n * API:\n *\n * - [InitColorSchemeScript API](https://mui.com/material-ui/api/init-color-scheme-script/)\n */\nfunction InitColorSchemeScript(props) {\n  const {\n    defaultMode = 'system',\n    defaultLightColorScheme = defaultConfig.defaultLightColorScheme,\n    defaultDarkColorScheme = defaultConfig.defaultDarkColorScheme,\n    modeStorageKey = defaultConfig.modeStorageKey,\n    colorSchemeStorageKey = defaultConfig.colorSchemeStorageKey,\n    attribute: initialAttribute = defaultConfig.attribute,\n    colorSchemeNode = 'document.documentElement',\n    nonce\n  } = props;\n  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_InitColorSchemeScript.default, {\n    defaultMode: defaultMode,\n    defaultLightColorScheme: defaultLightColorScheme,\n    defaultDarkColorScheme: defaultDarkColorScheme,\n    modeStorageKey: modeStorageKey,\n    colorSchemeStorageKey: colorSchemeStorageKey,\n    attribute: initialAttribute,\n    colorSchemeNode: colorSchemeNode,\n    nonce: nonce\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? InitColorSchemeScript.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * DOM attribute for applying a color scheme.\n   * @default 'data-mui-color-scheme'\n   * @example '.mode-%s' // for class based color scheme\n   * @example '[data-mode-%s]' // for data-attribute without '='\n   */\n  attribute: _propTypes.default.string,\n  /**\n   * The node (provided as string) used to attach the color-scheme attribute.\n   * @default 'document.documentElement'\n   */\n  colorSchemeNode: _propTypes.default.string,\n  /**\n   * localStorage key used to store `colorScheme`.\n   * @default 'mui-color-scheme'\n   */\n  colorSchemeStorageKey: _propTypes.default.string,\n  /**\n   * The default color scheme to be used in dark mode.\n   * @default 'dark'\n   */\n  defaultDarkColorScheme: _propTypes.default.string,\n  /**\n   * The default color scheme to be used in light mode.\n   * @default 'light'\n   */\n  defaultLightColorScheme: _propTypes.default.string,\n  /**\n   * The default mode when the storage is empty (user's first visit).\n   * @default 'system'\n   */\n  defaultMode: _propTypes.default.oneOf(['dark', 'light', 'system']),\n  /**\n   * localStorage key used to store `mode`.\n   * @default 'mui-mode'\n   */\n  modeStorageKey: _propTypes.default.string,\n  /**\n   * Nonce string to pass to the inline script for CSP headers.\n   */\n  nonce: _propTypes.default.string\n} : void 0;\nvar _default = exports.default = InitColorSchemeScript;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,aAAa,GAAGF,OAAO,CAACJ,OAAO,GAAG,KAAK,CAAC;AAChD,IAAIO,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,UAAU,GAAGV,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIU,sBAAsB,GAAGX,sBAAsB,CAACC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AACjG,IAAIW,WAAW,GAAGX,OAAO,CAAC,mBAAmB,CAAC;AAC9C,MAAMO,aAAa,GAAGF,OAAO,CAACE,aAAa,GAAG;EAC5CK,SAAS,EAAE,uBAAuB;EAClCC,qBAAqB,EAAE,kBAAkB;EACzCC,uBAAuB,EAAE,OAAO;EAChCC,sBAAsB,EAAE,MAAM;EAC9BC,cAAc,EAAE;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EACpC,MAAM;IACJC,WAAW,GAAG,QAAQ;IACtBL,uBAAuB,GAAGP,aAAa,CAACO,uBAAuB;IAC/DC,sBAAsB,GAAGR,aAAa,CAACQ,sBAAsB;IAC7DC,cAAc,GAAGT,aAAa,CAACS,cAAc;IAC7CH,qBAAqB,GAAGN,aAAa,CAACM,qBAAqB;IAC3DD,SAAS,EAAEQ,gBAAgB,GAAGb,aAAa,CAACK,SAAS;IACrDS,eAAe,GAAG,0BAA0B;IAC5CC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO,aAAa,CAAC,CAAC,EAAEP,WAAW,CAACY,GAAG,EAAEb,sBAAsB,CAACT,OAAO,EAAE;IACvEkB,WAAW,EAAEA,WAAW;IACxBL,uBAAuB,EAAEA,uBAAuB;IAChDC,sBAAsB,EAAEA,sBAAsB;IAC9CC,cAAc,EAAEA,cAAc;IAC9BH,qBAAqB,EAAEA,qBAAqB;IAC5CD,SAAS,EAAEQ,gBAAgB;IAC3BC,eAAe,EAAEA,eAAe;IAChCC,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,qBAAqB,CAACU,SAAS,CAAC,yBAAyB;EAC/F;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACEf,SAAS,EAAEH,UAAU,CAACR,OAAO,CAAC2B,MAAM;EACpC;AACF;AACA;AACA;EACEP,eAAe,EAAEZ,UAAU,CAACR,OAAO,CAAC2B,MAAM;EAC1C;AACF;AACA;AACA;EACEf,qBAAqB,EAAEJ,UAAU,CAACR,OAAO,CAAC2B,MAAM;EAChD;AACF;AACA;AACA;EACEb,sBAAsB,EAAEN,UAAU,CAACR,OAAO,CAAC2B,MAAM;EACjD;AACF;AACA;AACA;EACEd,uBAAuB,EAAEL,UAAU,CAACR,OAAO,CAAC2B,MAAM;EAClD;AACF;AACA;AACA;EACET,WAAW,EAAEV,UAAU,CAACR,OAAO,CAAC4B,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;EAClE;AACF;AACA;AACA;EACEb,cAAc,EAAEP,UAAU,CAACR,OAAO,CAAC2B,MAAM;EACzC;AACF;AACA;EACEN,KAAK,EAAEb,UAAU,CAACR,OAAO,CAAC2B;AAC5B,CAAC,GAAG,KAAK,CAAC;AACV,IAAIE,QAAQ,GAAGzB,OAAO,CAACJ,OAAO,GAAGgB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}