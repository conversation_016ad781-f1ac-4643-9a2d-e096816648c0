{"ast": null, "code": "export { default } from \"./spacing.js\";\nexport * from \"./spacing.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/system/esm/spacing/index.js"], "sourcesContent": ["export { default } from \"./spacing.js\";\nexport * from \"./spacing.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}