{"ast": null, "code": "import useEventCallback from '@mui/utils/useEventCallback';\nimport { useUtils } from \"../useUtils.js\";\nimport { cleanDigitSectionValue, getLetterEditingOptions, removeLocalizedDigits } from \"./useField.utils.js\";\n\n/**\n * Returns the `onKeyDown` handler to pass to the root element of the field.\n */\nexport function useFieldRootHandleKeyDown(parameters) {\n  const utils = useUtils();\n  const {\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults: {\n      minutesStep,\n      disabled,\n      readOnly\n    },\n    stateResponse: {\n      // States and derived states\n      state,\n      value,\n      activeSectionIndex,\n      parsedSelectedSections,\n      sectionsValueBoundaries,\n      localizedDigits,\n      timezone,\n      sectionOrder,\n      // Methods to update the states\n      clearValue,\n      clearActiveSection,\n      setSelectedSections,\n      updateSectionValue\n    }\n  } = parameters;\n  return useEventCallback(event => {\n    if (disabled) {\n      return;\n    }\n\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select all\n      case (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'A' && !event.shiftKey && !event.altKey:\n        {\n          // prevent default to make sure that the next line \"select all\" while updating\n          // the internal state at the same time.\n          event.preventDefault();\n          setSelectedSections('all');\n          break;\n        }\n\n      // Move selection to next section\n      case event.key === 'ArrowRight':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.startIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.endIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].rightIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Move selection to previous section\n      case event.key === 'ArrowLeft':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.endIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.startIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].leftIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Reset the value of the selected section\n      case event.key === 'Delete':\n        {\n          event.preventDefault();\n          if (readOnly) {\n            break;\n          }\n          if (parsedSelectedSections == null || parsedSelectedSections === 'all') {\n            clearValue();\n          } else {\n            clearActiveSection();\n          }\n          break;\n        }\n\n      // Increment / decrement the selected section value\n      case ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key):\n        {\n          event.preventDefault();\n          if (readOnly || activeSectionIndex == null) {\n            break;\n          }\n\n          // if all sections are selected, mark the currently editing one as selected\n          if (parsedSelectedSections === 'all') {\n            setSelectedSections(activeSectionIndex);\n          }\n          const activeSection = state.sections[activeSectionIndex];\n          const newSectionValue = adjustSectionValue(utils, timezone, activeSection, event.key, sectionsValueBoundaries, localizedDigits, fieldValueManager.getDateFromSection(value, activeSection), {\n            minutesStep\n          });\n          updateSectionValue({\n            section: activeSection,\n            newSectionValue,\n            shouldGoToNextSection: false\n          });\n          break;\n        }\n    }\n  });\n}\nfunction getDeltaFromKeyCode(keyCode) {\n  switch (keyCode) {\n    case 'ArrowUp':\n      return 1;\n    case 'ArrowDown':\n      return -1;\n    case 'PageUp':\n      return 5;\n    case 'PageDown':\n      return -5;\n    default:\n      return 0;\n  }\n}\nfunction adjustSectionValue(utils, timezone, section, keyCode, sectionsValueBoundaries, localizedDigits, activeDate, stepsAttributes) {\n  const delta = getDeltaFromKeyCode(keyCode);\n  const isStart = keyCode === 'Home';\n  const isEnd = keyCode === 'End';\n  const shouldSetAbsolute = section.value === '' || isStart || isEnd;\n  const adjustDigitSection = () => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: activeDate,\n      format: section.format,\n      contentType: section.contentType\n    });\n    const getCleanValue = value => cleanDigitSectionValue(utils, value, sectionBoundaries, localizedDigits, section);\n    const step = section.type === 'minutes' && stepsAttributes?.minutesStep ? stepsAttributes.minutesStep : 1;\n    let newSectionValueNumber;\n    if (shouldSetAbsolute) {\n      if (section.type === 'year' && !isEnd && !isStart) {\n        return utils.formatByString(utils.date(undefined, timezone), section.format);\n      }\n      if (delta > 0 || isStart) {\n        newSectionValueNumber = sectionBoundaries.minimum;\n      } else {\n        newSectionValueNumber = sectionBoundaries.maximum;\n      }\n    } else {\n      const currentSectionValue = parseInt(removeLocalizedDigits(section.value, localizedDigits), 10);\n      newSectionValueNumber = currentSectionValue + delta * step;\n    }\n    if (newSectionValueNumber % step !== 0) {\n      if (delta < 0 || isStart) {\n        newSectionValueNumber += step - (step + newSectionValueNumber) % step; // for JS -3 % 5 = -3 (should be 2)\n      }\n      if (delta > 0 || isEnd) {\n        newSectionValueNumber -= newSectionValueNumber % step;\n      }\n    }\n    if (newSectionValueNumber > sectionBoundaries.maximum) {\n      return getCleanValue(sectionBoundaries.minimum + (newSectionValueNumber - sectionBoundaries.maximum - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    if (newSectionValueNumber < sectionBoundaries.minimum) {\n      return getCleanValue(sectionBoundaries.maximum - (sectionBoundaries.minimum - newSectionValueNumber - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    return getCleanValue(newSectionValueNumber);\n  };\n  const adjustLetterSection = () => {\n    const options = getLetterEditingOptions(utils, timezone, section.type, section.format);\n    if (options.length === 0) {\n      return section.value;\n    }\n    if (shouldSetAbsolute) {\n      if (delta > 0 || isStart) {\n        return options[0];\n      }\n      return options[options.length - 1];\n    }\n    const currentOptionIndex = options.indexOf(section.value);\n    const newOptionIndex = (currentOptionIndex + delta) % options.length;\n    const clampedIndex = (newOptionIndex + options.length) % options.length;\n    return options[clampedIndex];\n  };\n  if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {\n    return adjustDigitSection();\n  }\n  return adjustLetterSection();\n}", "map": {"version": 3, "names": ["useEventCallback", "useUtils", "cleanDigitSectionValue", "getLetterEditingOptions", "removeLocalizedDigits", "useFieldRootHandleKeyDown", "parameters", "utils", "manager", "internal_fieldValueManager", "field<PERSON><PERSON>ueManager", "internalPropsWithDefaults", "minutesStep", "disabled", "readOnly", "stateResponse", "state", "value", "activeSectionIndex", "parsedSelectedSections", "sectionsValueBoundaries", "localizedDigits", "timezone", "sectionOrder", "clearValue", "clearActiveSection", "setSelectedSections", "updateSectionValue", "event", "ctrl<PERSON>ey", "metaKey", "String", "fromCharCode", "keyCode", "shift<PERSON>ey", "altKey", "preventDefault", "key", "startIndex", "endIndex", "nextSectionIndex", "neighbors", "rightIndex", "leftIndex", "includes", "activeSection", "sections", "newSectionValue", "adjustSectionValue", "getDateFromSection", "section", "shouldGoToNextSection", "getDeltaFromKeyCode", "activeDate", "stepsAttributes", "delta", "isStart", "isEnd", "shouldSetAbsolute", "adjustDigitSection", "sectionBoundaries", "type", "currentDate", "format", "contentType", "getCleanValue", "step", "newSectionValueNumber", "formatByString", "date", "undefined", "minimum", "maximum", "currentSectionValue", "parseInt", "adjustLetterSection", "options", "length", "currentOptionIndex", "indexOf", "newOptionIndex", "clampedIndex"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useFieldRootHandleKeyDown.js"], "sourcesContent": ["import useEventCallback from '@mui/utils/useEventCallback';\nimport { useUtils } from \"../useUtils.js\";\nimport { cleanDigitSectionValue, getLetterEditingOptions, removeLocalizedDigits } from \"./useField.utils.js\";\n\n/**\n * Returns the `onKeyDown` handler to pass to the root element of the field.\n */\nexport function useFieldRootHandleKeyDown(parameters) {\n  const utils = useUtils();\n  const {\n    manager: {\n      internal_fieldValueManager: fieldValueManager\n    },\n    internalPropsWithDefaults: {\n      minutesStep,\n      disabled,\n      readOnly\n    },\n    stateResponse: {\n      // States and derived states\n      state,\n      value,\n      activeSectionIndex,\n      parsedSelectedSections,\n      sectionsValueBoundaries,\n      localizedDigits,\n      timezone,\n      sectionOrder,\n      // Methods to update the states\n      clearValue,\n      clearActiveSection,\n      setSelectedSections,\n      updateSectionValue\n    }\n  } = parameters;\n  return useEventCallback(event => {\n    if (disabled) {\n      return;\n    }\n\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select all\n      case (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'A' && !event.shiftKey && !event.altKey:\n        {\n          // prevent default to make sure that the next line \"select all\" while updating\n          // the internal state at the same time.\n          event.preventDefault();\n          setSelectedSections('all');\n          break;\n        }\n\n      // Move selection to next section\n      case event.key === 'ArrowRight':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.startIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.endIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].rightIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Move selection to previous section\n      case event.key === 'ArrowLeft':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.endIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.startIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].leftIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Reset the value of the selected section\n      case event.key === 'Delete':\n        {\n          event.preventDefault();\n          if (readOnly) {\n            break;\n          }\n          if (parsedSelectedSections == null || parsedSelectedSections === 'all') {\n            clearValue();\n          } else {\n            clearActiveSection();\n          }\n          break;\n        }\n\n      // Increment / decrement the selected section value\n      case ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key):\n        {\n          event.preventDefault();\n          if (readOnly || activeSectionIndex == null) {\n            break;\n          }\n\n          // if all sections are selected, mark the currently editing one as selected\n          if (parsedSelectedSections === 'all') {\n            setSelectedSections(activeSectionIndex);\n          }\n          const activeSection = state.sections[activeSectionIndex];\n          const newSectionValue = adjustSectionValue(utils, timezone, activeSection, event.key, sectionsValueBoundaries, localizedDigits, fieldValueManager.getDateFromSection(value, activeSection), {\n            minutesStep\n          });\n          updateSectionValue({\n            section: activeSection,\n            newSectionValue,\n            shouldGoToNextSection: false\n          });\n          break;\n        }\n    }\n  });\n}\nfunction getDeltaFromKeyCode(keyCode) {\n  switch (keyCode) {\n    case 'ArrowUp':\n      return 1;\n    case 'ArrowDown':\n      return -1;\n    case 'PageUp':\n      return 5;\n    case 'PageDown':\n      return -5;\n    default:\n      return 0;\n  }\n}\nfunction adjustSectionValue(utils, timezone, section, keyCode, sectionsValueBoundaries, localizedDigits, activeDate, stepsAttributes) {\n  const delta = getDeltaFromKeyCode(keyCode);\n  const isStart = keyCode === 'Home';\n  const isEnd = keyCode === 'End';\n  const shouldSetAbsolute = section.value === '' || isStart || isEnd;\n  const adjustDigitSection = () => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: activeDate,\n      format: section.format,\n      contentType: section.contentType\n    });\n    const getCleanValue = value => cleanDigitSectionValue(utils, value, sectionBoundaries, localizedDigits, section);\n    const step = section.type === 'minutes' && stepsAttributes?.minutesStep ? stepsAttributes.minutesStep : 1;\n    let newSectionValueNumber;\n    if (shouldSetAbsolute) {\n      if (section.type === 'year' && !isEnd && !isStart) {\n        return utils.formatByString(utils.date(undefined, timezone), section.format);\n      }\n      if (delta > 0 || isStart) {\n        newSectionValueNumber = sectionBoundaries.minimum;\n      } else {\n        newSectionValueNumber = sectionBoundaries.maximum;\n      }\n    } else {\n      const currentSectionValue = parseInt(removeLocalizedDigits(section.value, localizedDigits), 10);\n      newSectionValueNumber = currentSectionValue + delta * step;\n    }\n    if (newSectionValueNumber % step !== 0) {\n      if (delta < 0 || isStart) {\n        newSectionValueNumber += step - (step + newSectionValueNumber) % step; // for JS -3 % 5 = -3 (should be 2)\n      }\n      if (delta > 0 || isEnd) {\n        newSectionValueNumber -= newSectionValueNumber % step;\n      }\n    }\n    if (newSectionValueNumber > sectionBoundaries.maximum) {\n      return getCleanValue(sectionBoundaries.minimum + (newSectionValueNumber - sectionBoundaries.maximum - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    if (newSectionValueNumber < sectionBoundaries.minimum) {\n      return getCleanValue(sectionBoundaries.maximum - (sectionBoundaries.minimum - newSectionValueNumber - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    return getCleanValue(newSectionValueNumber);\n  };\n  const adjustLetterSection = () => {\n    const options = getLetterEditingOptions(utils, timezone, section.type, section.format);\n    if (options.length === 0) {\n      return section.value;\n    }\n    if (shouldSetAbsolute) {\n      if (delta > 0 || isStart) {\n        return options[0];\n      }\n      return options[options.length - 1];\n    }\n    const currentOptionIndex = options.indexOf(section.value);\n    const newOptionIndex = (currentOptionIndex + delta) % options.length;\n    const clampedIndex = (newOptionIndex + options.length) % options.length;\n    return options[clampedIndex];\n  };\n  if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {\n    return adjustDigitSection();\n  }\n  return adjustLetterSection();\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,QAAQ,qBAAqB;;AAE5G;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAACC,UAAU,EAAE;EACpD,MAAMC,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJO,OAAO,EAAE;MACPC,0BAA0B,EAAEC;IAC9B,CAAC;IACDC,yBAAyB,EAAE;MACzBC,WAAW;MACXC,QAAQ;MACRC;IACF,CAAC;IACDC,aAAa,EAAE;MACb;MACAC,KAAK;MACLC,KAAK;MACLC,kBAAkB;MAClBC,sBAAsB;MACtBC,uBAAuB;MACvBC,eAAe;MACfC,QAAQ;MACRC,YAAY;MACZ;MACAC,UAAU;MACVC,kBAAkB;MAClBC,mBAAmB;MACnBC;IACF;EACF,CAAC,GAAGrB,UAAU;EACd,OAAON,gBAAgB,CAAC4B,KAAK,IAAI;IAC/B,IAAIf,QAAQ,EAAE;MACZ;IACF;;IAEA;IACA,QAAQ,IAAI;MACV;MACA,KAAK,CAACe,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAAKC,MAAM,CAACC,YAAY,CAACJ,KAAK,CAACK,OAAO,CAAC,KAAK,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,IAAI,CAACN,KAAK,CAACO,MAAM;QACrH;UACE;UACA;UACAP,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtBV,mBAAmB,CAAC,KAAK,CAAC;UAC1B;QACF;;MAEF;MACA,KAAKE,KAAK,CAACS,GAAG,KAAK,YAAY;QAC7B;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAIjB,sBAAsB,IAAI,IAAI,EAAE;YAClCO,mBAAmB,CAACH,YAAY,CAACe,UAAU,CAAC;UAC9C,CAAC,MAAM,IAAInB,sBAAsB,KAAK,KAAK,EAAE;YAC3CO,mBAAmB,CAACH,YAAY,CAACgB,QAAQ,CAAC;UAC5C,CAAC,MAAM;YACL,MAAMC,gBAAgB,GAAGjB,YAAY,CAACkB,SAAS,CAACtB,sBAAsB,CAAC,CAACuB,UAAU;YAClF,IAAIF,gBAAgB,KAAK,IAAI,EAAE;cAC7Bd,mBAAmB,CAACc,gBAAgB,CAAC;YACvC;UACF;UACA;QACF;;MAEF;MACA,KAAKZ,KAAK,CAACS,GAAG,KAAK,WAAW;QAC5B;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAIjB,sBAAsB,IAAI,IAAI,EAAE;YAClCO,mBAAmB,CAACH,YAAY,CAACgB,QAAQ,CAAC;UAC5C,CAAC,MAAM,IAAIpB,sBAAsB,KAAK,KAAK,EAAE;YAC3CO,mBAAmB,CAACH,YAAY,CAACe,UAAU,CAAC;UAC9C,CAAC,MAAM;YACL,MAAME,gBAAgB,GAAGjB,YAAY,CAACkB,SAAS,CAACtB,sBAAsB,CAAC,CAACwB,SAAS;YACjF,IAAIH,gBAAgB,KAAK,IAAI,EAAE;cAC7Bd,mBAAmB,CAACc,gBAAgB,CAAC;YACvC;UACF;UACA;QACF;;MAEF;MACA,KAAKZ,KAAK,CAACS,GAAG,KAAK,QAAQ;QACzB;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAItB,QAAQ,EAAE;YACZ;UACF;UACA,IAAIK,sBAAsB,IAAI,IAAI,IAAIA,sBAAsB,KAAK,KAAK,EAAE;YACtEK,UAAU,CAAC,CAAC;UACd,CAAC,MAAM;YACLC,kBAAkB,CAAC,CAAC;UACtB;UACA;QACF;;MAEF;MACA,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACmB,QAAQ,CAAChB,KAAK,CAACS,GAAG,CAAC;QACpF;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAItB,QAAQ,IAAII,kBAAkB,IAAI,IAAI,EAAE;YAC1C;UACF;;UAEA;UACA,IAAIC,sBAAsB,KAAK,KAAK,EAAE;YACpCO,mBAAmB,CAACR,kBAAkB,CAAC;UACzC;UACA,MAAM2B,aAAa,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC5B,kBAAkB,CAAC;UACxD,MAAM6B,eAAe,GAAGC,kBAAkB,CAACzC,KAAK,EAAEe,QAAQ,EAAEuB,aAAa,EAAEjB,KAAK,CAACS,GAAG,EAAEjB,uBAAuB,EAAEC,eAAe,EAAEX,iBAAiB,CAACuC,kBAAkB,CAAChC,KAAK,EAAE4B,aAAa,CAAC,EAAE;YAC1LjC;UACF,CAAC,CAAC;UACFe,kBAAkB,CAAC;YACjBuB,OAAO,EAAEL,aAAa;YACtBE,eAAe;YACfI,qBAAqB,EAAE;UACzB,CAAC,CAAC;UACF;QACF;IACJ;EACF,CAAC,CAAC;AACJ;AACA,SAASC,mBAAmBA,CAACnB,OAAO,EAAE;EACpC,QAAQA,OAAO;IACb,KAAK,SAAS;MACZ,OAAO,CAAC;IACV,KAAK,WAAW;MACd,OAAO,CAAC,CAAC;IACX,KAAK,QAAQ;MACX,OAAO,CAAC;IACV,KAAK,UAAU;MACb,OAAO,CAAC,CAAC;IACX;MACE,OAAO,CAAC;EACZ;AACF;AACA,SAASe,kBAAkBA,CAACzC,KAAK,EAAEe,QAAQ,EAAE4B,OAAO,EAAEjB,OAAO,EAAEb,uBAAuB,EAAEC,eAAe,EAAEgC,UAAU,EAAEC,eAAe,EAAE;EACpI,MAAMC,KAAK,GAAGH,mBAAmB,CAACnB,OAAO,CAAC;EAC1C,MAAMuB,OAAO,GAAGvB,OAAO,KAAK,MAAM;EAClC,MAAMwB,KAAK,GAAGxB,OAAO,KAAK,KAAK;EAC/B,MAAMyB,iBAAiB,GAAGR,OAAO,CAACjC,KAAK,KAAK,EAAE,IAAIuC,OAAO,IAAIC,KAAK;EAClE,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,iBAAiB,GAAGxC,uBAAuB,CAAC8B,OAAO,CAACW,IAAI,CAAC,CAAC;MAC9DC,WAAW,EAAET,UAAU;MACvBU,MAAM,EAAEb,OAAO,CAACa,MAAM;MACtBC,WAAW,EAAEd,OAAO,CAACc;IACvB,CAAC,CAAC;IACF,MAAMC,aAAa,GAAGhD,KAAK,IAAIf,sBAAsB,CAACK,KAAK,EAAEU,KAAK,EAAE2C,iBAAiB,EAAEvC,eAAe,EAAE6B,OAAO,CAAC;IAChH,MAAMgB,IAAI,GAAGhB,OAAO,CAACW,IAAI,KAAK,SAAS,IAAIP,eAAe,EAAE1C,WAAW,GAAG0C,eAAe,CAAC1C,WAAW,GAAG,CAAC;IACzG,IAAIuD,qBAAqB;IACzB,IAAIT,iBAAiB,EAAE;MACrB,IAAIR,OAAO,CAACW,IAAI,KAAK,MAAM,IAAI,CAACJ,KAAK,IAAI,CAACD,OAAO,EAAE;QACjD,OAAOjD,KAAK,CAAC6D,cAAc,CAAC7D,KAAK,CAAC8D,IAAI,CAACC,SAAS,EAAEhD,QAAQ,CAAC,EAAE4B,OAAO,CAACa,MAAM,CAAC;MAC9E;MACA,IAAIR,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxBW,qBAAqB,GAAGP,iBAAiB,CAACW,OAAO;MACnD,CAAC,MAAM;QACLJ,qBAAqB,GAAGP,iBAAiB,CAACY,OAAO;MACnD;IACF,CAAC,MAAM;MACL,MAAMC,mBAAmB,GAAGC,QAAQ,CAACtE,qBAAqB,CAAC8C,OAAO,CAACjC,KAAK,EAAEI,eAAe,CAAC,EAAE,EAAE,CAAC;MAC/F8C,qBAAqB,GAAGM,mBAAmB,GAAGlB,KAAK,GAAGW,IAAI;IAC5D;IACA,IAAIC,qBAAqB,GAAGD,IAAI,KAAK,CAAC,EAAE;MACtC,IAAIX,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxBW,qBAAqB,IAAID,IAAI,GAAG,CAACA,IAAI,GAAGC,qBAAqB,IAAID,IAAI,CAAC,CAAC;MACzE;MACA,IAAIX,KAAK,GAAG,CAAC,IAAIE,KAAK,EAAE;QACtBU,qBAAqB,IAAIA,qBAAqB,GAAGD,IAAI;MACvD;IACF;IACA,IAAIC,qBAAqB,GAAGP,iBAAiB,CAACY,OAAO,EAAE;MACrD,OAAOP,aAAa,CAACL,iBAAiB,CAACW,OAAO,GAAG,CAACJ,qBAAqB,GAAGP,iBAAiB,CAACY,OAAO,GAAG,CAAC,KAAKZ,iBAAiB,CAACY,OAAO,GAAGZ,iBAAiB,CAACW,OAAO,GAAG,CAAC,CAAC,CAAC;IACzK;IACA,IAAIJ,qBAAqB,GAAGP,iBAAiB,CAACW,OAAO,EAAE;MACrD,OAAON,aAAa,CAACL,iBAAiB,CAACY,OAAO,GAAG,CAACZ,iBAAiB,CAACW,OAAO,GAAGJ,qBAAqB,GAAG,CAAC,KAAKP,iBAAiB,CAACY,OAAO,GAAGZ,iBAAiB,CAACW,OAAO,GAAG,CAAC,CAAC,CAAC;IACzK;IACA,OAAON,aAAa,CAACE,qBAAqB,CAAC;EAC7C,CAAC;EACD,MAAMQ,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAGzE,uBAAuB,CAACI,KAAK,EAAEe,QAAQ,EAAE4B,OAAO,CAACW,IAAI,EAAEX,OAAO,CAACa,MAAM,CAAC;IACtF,IAAIa,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO3B,OAAO,CAACjC,KAAK;IACtB;IACA,IAAIyC,iBAAiB,EAAE;MACrB,IAAIH,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxB,OAAOoB,OAAO,CAAC,CAAC,CAAC;MACnB;MACA,OAAOA,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC;IACpC;IACA,MAAMC,kBAAkB,GAAGF,OAAO,CAACG,OAAO,CAAC7B,OAAO,CAACjC,KAAK,CAAC;IACzD,MAAM+D,cAAc,GAAG,CAACF,kBAAkB,GAAGvB,KAAK,IAAIqB,OAAO,CAACC,MAAM;IACpE,MAAMI,YAAY,GAAG,CAACD,cAAc,GAAGJ,OAAO,CAACC,MAAM,IAAID,OAAO,CAACC,MAAM;IACvE,OAAOD,OAAO,CAACK,YAAY,CAAC;EAC9B,CAAC;EACD,IAAI/B,OAAO,CAACc,WAAW,KAAK,OAAO,IAAId,OAAO,CAACc,WAAW,KAAK,mBAAmB,EAAE;IAClF,OAAOL,kBAAkB,CAAC,CAAC;EAC7B;EACA,OAAOgB,mBAAmB,CAAC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}