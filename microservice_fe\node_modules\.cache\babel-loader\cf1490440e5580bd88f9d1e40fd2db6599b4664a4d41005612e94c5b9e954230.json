{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.stringifyTheme = stringifyTheme;\nvar _deepmerge = require(\"@mui/utils/deepmerge\");\n/* eslint-disable import/prefer-default-export */\n\nfunction isSerializable(val) {\n  return (0, _deepmerge.isPlainObject)(val) || typeof val === 'undefined' || typeof val === 'string' || typeof val === 'boolean' || typeof val === 'number' || Array.isArray(val);\n}\n\n/**\n * `baseTheme` usually comes from `createTheme()` or `extendTheme()`.\n *\n * This function is intended to be used with zero-runtime CSS-in-JS like Pigment CSS\n * For example, in a Next.js project:\n *\n * ```js\n * // next.config.js\n * const { extendTheme } = require('@mui/material/styles');\n *\n * const theme = extendTheme();\n * // `.toRuntimeSource` is Pigment CSS specific to create a theme that is available at runtime.\n * theme.toRuntimeSource = stringifyTheme;\n *\n * module.exports = withPigment({\n *  theme,\n * });\n * ```\n */\nfunction stringifyTheme(baseTheme = {}) {\n  const serializableTheme = {\n    ...baseTheme\n  };\n  function serializeTheme(object) {\n    const array = Object.entries(object);\n    // eslint-disable-next-line no-plusplus\n    for (let index = 0; index < array.length; index++) {\n      const [key, value] = array[index];\n      if (!isSerializable(value) || key.startsWith('unstable_')) {\n        delete object[key];\n      } else if ((0, _deepmerge.isPlainObject)(value)) {\n        object[key] = {\n          ...value\n        };\n        serializeTheme(object[key]);\n      }\n    }\n  }\n  serializeTheme(serializableTheme);\n  return `import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(serializableTheme, null, 2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "stringifyTheme", "_deepmerge", "require", "isSerializable", "val", "isPlainObject", "Array", "isArray", "baseTheme", "serializableTheme", "serializeTheme", "object", "array", "entries", "index", "length", "key", "startsWith", "JSON", "stringify"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/material/styles/stringifyTheme.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.stringifyTheme = stringifyTheme;\nvar _deepmerge = require(\"@mui/utils/deepmerge\");\n/* eslint-disable import/prefer-default-export */\n\nfunction isSerializable(val) {\n  return (0, _deepmerge.isPlainObject)(val) || typeof val === 'undefined' || typeof val === 'string' || typeof val === 'boolean' || typeof val === 'number' || Array.isArray(val);\n}\n\n/**\n * `baseTheme` usually comes from `createTheme()` or `extendTheme()`.\n *\n * This function is intended to be used with zero-runtime CSS-in-JS like Pigment CSS\n * For example, in a Next.js project:\n *\n * ```js\n * // next.config.js\n * const { extendTheme } = require('@mui/material/styles');\n *\n * const theme = extendTheme();\n * // `.toRuntimeSource` is Pigment CSS specific to create a theme that is available at runtime.\n * theme.toRuntimeSource = stringifyTheme;\n *\n * module.exports = withPigment({\n *  theme,\n * });\n * ```\n */\nfunction stringifyTheme(baseTheme = {}) {\n  const serializableTheme = {\n    ...baseTheme\n  };\n  function serializeTheme(object) {\n    const array = Object.entries(object);\n    // eslint-disable-next-line no-plusplus\n    for (let index = 0; index < array.length; index++) {\n      const [key, value] = array[index];\n      if (!isSerializable(value) || key.startsWith('unstable_')) {\n        delete object[key];\n      } else if ((0, _deepmerge.isPlainObject)(value)) {\n        object[key] = {\n          ...value\n        };\n        serializeTheme(object[key]);\n      }\n    }\n  }\n  serializeTheme(serializableTheme);\n  return `import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(serializableTheme, null, 2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,cAAc,GAAGA,cAAc;AACvC,IAAIC,UAAU,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAChD;;AAEA,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,OAAO,CAAC,CAAC,EAAEH,UAAU,CAACI,aAAa,EAAED,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC;AACjL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,cAAcA,CAACQ,SAAS,GAAG,CAAC,CAAC,EAAE;EACtC,MAAMC,iBAAiB,GAAG;IACxB,GAAGD;EACL,CAAC;EACD,SAASE,cAAcA,CAACC,MAAM,EAAE;IAC9B,MAAMC,KAAK,GAAGhB,MAAM,CAACiB,OAAO,CAACF,MAAM,CAAC;IACpC;IACA,KAAK,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,CAACG,MAAM,EAAED,KAAK,EAAE,EAAE;MACjD,MAAM,CAACE,GAAG,EAAEjB,KAAK,CAAC,GAAGa,KAAK,CAACE,KAAK,CAAC;MACjC,IAAI,CAACX,cAAc,CAACJ,KAAK,CAAC,IAAIiB,GAAG,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;QACzD,OAAON,MAAM,CAACK,GAAG,CAAC;MACpB,CAAC,MAAM,IAAI,CAAC,CAAC,EAAEf,UAAU,CAACI,aAAa,EAAEN,KAAK,CAAC,EAAE;QAC/CY,MAAM,CAACK,GAAG,CAAC,GAAG;UACZ,GAAGjB;QACL,CAAC;QACDW,cAAc,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC;MAC7B;IACF;EACF;EACAN,cAAc,CAACD,iBAAiB,CAAC;EACjC,OAAO;AACT;AACA,gBAAgBS,IAAI,CAACC,SAAS,CAACV,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA,sBAAsB;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}