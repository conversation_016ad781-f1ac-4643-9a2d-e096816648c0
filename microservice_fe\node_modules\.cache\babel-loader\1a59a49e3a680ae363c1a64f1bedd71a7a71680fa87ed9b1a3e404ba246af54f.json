{"ast": null, "code": "import * as React from 'react';\nexport const PickerFieldPrivateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") PickerFieldPrivateContext.displayName = \"PickerFieldPrivateContext\";\nexport function useNullableFieldPrivateContext() {\n  return React.useContext(PickerFieldPrivateContext);\n}", "map": {"version": 3, "names": ["React", "PickerFieldPrivateContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useNullableFieldPrivateContext", "useContext"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/x-date-pickers/esm/internals/hooks/useNullableFieldPrivateContext.js"], "sourcesContent": ["import * as React from 'react';\nexport const PickerFieldPrivateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") PickerFieldPrivateContext.displayName = \"PickerFieldPrivateContext\";\nexport function useNullableFieldPrivateContext() {\n  return React.useContext(PickerFieldPrivateContext);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,yBAAyB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC/E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEJ,yBAAyB,CAACK,WAAW,GAAG,2BAA2B;AAC9G,OAAO,SAASC,8BAA8BA,CAAA,EAAG;EAC/C,OAAOP,KAAK,CAACQ,UAAU,CAACP,yBAAyB,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}