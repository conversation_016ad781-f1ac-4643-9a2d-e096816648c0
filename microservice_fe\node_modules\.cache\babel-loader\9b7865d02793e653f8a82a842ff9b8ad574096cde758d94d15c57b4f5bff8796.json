{"ast": null, "code": "export default function deprecatedPropType(validator, reason) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n  return (props, propName, componentName, location, propFullName) => {\n    const componentNameSafe = componentName || '<<anonymous>>';\n    const propFullNameSafe = propFullName || propName;\n    if (typeof props[propName] !== 'undefined') {\n      return new Error(`The ${location} \\`${propFullNameSafe}\\` of ` + `\\`${componentNameSafe}\\` is deprecated. ${reason}`);\n    }\n    return null;\n  };\n}", "map": {"version": 3, "names": ["deprecatedPropType", "validator", "reason", "process", "env", "NODE_ENV", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "componentNameSafe", "propFullNameSafe", "Error"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@mui/utils/esm/deprecatedPropType/deprecatedPropType.js"], "sourcesContent": ["export default function deprecatedPropType(validator, reason) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n  return (props, propName, componentName, location, propFullName) => {\n    const componentNameSafe = componentName || '<<anonymous>>';\n    const propFullNameSafe = propFullName || propName;\n    if (typeof props[propName] !== 'undefined') {\n      return new Error(`The ${location} \\`${propFullNameSafe}\\` of ` + `\\`${componentNameSafe}\\` is deprecated. ${reason}`);\n    }\n    return null;\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAACC,SAAS,EAAEC,MAAM,EAAE;EAC5D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,MAAM,IAAI;EACnB;EACA,OAAO,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,KAAK;IACjE,MAAMC,iBAAiB,GAAGH,aAAa,IAAI,eAAe;IAC1D,MAAMI,gBAAgB,GAAGF,YAAY,IAAIH,QAAQ;IACjD,IAAI,OAAOD,KAAK,CAACC,QAAQ,CAAC,KAAK,WAAW,EAAE;MAC1C,OAAO,IAAIM,KAAK,CAAC,OAAOJ,QAAQ,MAAMG,gBAAgB,QAAQ,GAAG,KAAKD,iBAAiB,qBAAqBT,MAAM,EAAE,CAAC;IACvH;IACA,OAAO,IAAI;EACb,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}